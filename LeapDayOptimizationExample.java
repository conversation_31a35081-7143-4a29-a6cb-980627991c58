import java.util.Set;

/**
 * 闰日优化示例
 * 展示如何使用优化后的证件有效期校验工具
 */
public class LeapDayOptimizationExample {
    
    public static void main(String[] args) {
        // 原来的方式：需要预定义大量的特殊日期组合
        System.out.println("=== 原来的方式（需要大量预定义组合） ===");
        IdentityValidityUtils.Rule oldRule = createOldStyleRule();
        
        // 新的方式：启用智能闰日校验
        System.out.println("\n=== 优化后的方式（智能闰日校验） ===");
        IdentityValidityUtils.Rule newRule = createOptimizedRule();
        
        // 测试各种闰日相关的日期组合
        String[] testCases = {
            "20200228-20250227", // 开始0228，结束0227
            "20200228-20250228", // 开始0228，结束0228
            "20200228-20250229", // 开始0228，结束0229
            "20200228-20250301", // 开始0228，结束0301
            "20200228-20250302", // 开始0228，结束0302
            "20200229-20250227", // 开始0229，结束0227
            "20200229-20250228", // 开始0229，结束0228
            "20200229-20250229", // 开始0229，结束0229
            "20200229-20250301", // 开始0229，结束0301
            "20200229-20250302", // 开始0229，结束0302
            "20200227-20250228", // 结束0228，开始0227
            "20200301-20250228", // 结束0228，开始0301
            "20200302-20250228", // 结束0228，开始0302
            "20200227-20250229", // 结束0229，开始0227
            "20200301-20250229", // 结束0229，开始0301
            "20200302-20250229"  // 结束0229，开始0302
        };
        
        System.out.println("测试用例数量: " + testCases.length);
        System.out.println("原来需要在uniqueValidity中预定义的组合数量: " + testCases.length);
        System.out.println("优化后需要预定义的组合数量: 0（通过算法智能判断）");
        
        System.out.println("\n详细测试结果:");
        for (String testCase : testCases) {
            boolean oldResult = IdentityValidityUtils.checkValidity(oldRule, testCase).isValid();
            boolean newResult = IdentityValidityUtils.checkValidity(newRule, testCase).isValid();
            
            System.out.printf("%-20s | 原方式: %-5s | 新方式: %-5s | %s%n", 
                testCase, oldResult, newResult, 
                oldResult == newResult ? "✓" : "✗");
        }
    }
    
    /**
     * 创建原来风格的规则（需要大量预定义组合）
     */
    private static IdentityValidityUtils.Rule createOldStyleRule() {
        IdentityValidityUtils.Rule rule = new IdentityValidityUtils.Rule();
        rule.setValidYearDiff(Set.of(5, 10, 20));
        rule.setDayOffsetTolerance(1);
        rule.setSupportLongTerm(false);
        rule.setMinRemainingDays(0);
        rule.setEnableLeapDayValidation(false); // 不启用新的闰日校验
        
        // 需要预定义所有可能的闰日组合
        Set<String> uniqueValidity = Set.of(
            "0228-0227", "0228-0228", "0228-0229", "0228-0301", "0228-0302",
            "0229-0227", "0229-0228", "0229-0229", "0229-0301", "0229-0302",
            "0227-0228", "0301-0228", "0302-0228",
            "0227-0229", "0301-0229", "0302-0229"
        );
        rule.setUniqueValidity(uniqueValidity);
        
        return rule;
    }
    
    /**
     * 创建优化后的规则（智能闰日校验）
     */
    private static IdentityValidityUtils.Rule createOptimizedRule() {
        IdentityValidityUtils.Rule rule = new IdentityValidityUtils.Rule();
        rule.setValidYearDiff(Set.of(5, 10, 20));
        rule.setDayOffsetTolerance(1);
        rule.setSupportLongTerm(false);
        rule.setMinRemainingDays(0);
        rule.setEnableLeapDayValidation(true); // 启用智能闰日校验
        
        // 不需要预定义闰日组合，uniqueValidity可以用于其他特殊情况
        rule.setUniqueValidity(Set.of()); // 空集合
        
        return rule;
    }
}
