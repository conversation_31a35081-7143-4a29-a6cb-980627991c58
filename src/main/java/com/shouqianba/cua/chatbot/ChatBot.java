package com.shouqianba.cua.chatbot;

import com.shouqianba.cua.chatbot.client.ChatBotClient;
import com.shouqianba.cua.chatbot.message.Message;
import com.shouqianba.cua.chatbot.message.TextMessage;
import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.common.utils.WosaiStringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.Queue;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2024/8/7
 */
@Slf4j
public class ChatBot {

    /**
     * webhook地址
     */
    private String webhook;

    /**
     * 安全秘钥
     */
    private String secret;

    /**
     * 客户端
     */
    private ChatBotClient chatBotClient;

    private ReentrantLock lock = new ReentrantLock();
    private final Queue<TextMessage> messageQueue = new LinkedBlockingQueue<>();
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private int batchSize = 20;
    private int sendDelaySeconds = 60;

    public ChatBot(String webhook, String secret, ChatBotClient chatBotClient) {
        Assert.hasText(webhook, "webhook不能为空");
        Assert.notNull(chatBotClient, "chatBotClient不能为空");
        this.webhook = webhook;
        this.secret = secret;
        this.chatBotClient = chatBotClient;
        scheduler.scheduleWithFixedDelay(this::sendMessages, sendDelaySeconds, sendDelaySeconds, TimeUnit.SECONDS);
    }

    /**
     * 立即发送消息
     *
     * @param message
     */
    public void sendMessageImmediately(Message message) {
        try {
            chatBotClient.send(webhook, message, secret);
        } catch (Exception e) {
            log.warn("发送消息失败 {} {} {}", webhook, secret, message.toJsonMap(), e);
        }
    }

    /**
     * 延迟发送消息
     * 把多条消息聚合在一起发送
     *
     * @param textMessage
     */
    public void delaySendMessage(TextMessage textMessage) {
        String text = WosaiDateTimeUtils.format(System.currentTimeMillis(), "MM-dd HH:mm:ss") + " " + textMessage.getText();
        textMessage.setText(text);
        messageQueue.add(textMessage);
        if (messageQueue.size() >= batchSize) {
            sendMessages();
        }
    }

    private void sendMessages() {
        if (lock.tryLock()) {
            try {
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < batchSize && !messageQueue.isEmpty(); i++) {
                    TextMessage textMessage = messageQueue.poll();
                    builder.append(i + 1).append(". ").append(textMessage.getText()).append("\n");
                }
                if (WosaiStringUtils.isNotEmpty(builder.toString())) {
                    sendMessageImmediately(new TextMessage(builder.toString()));
                }
            } catch (Exception e) {
                log.warn("批量发送消息失败", e);
            } finally {
                lock.unlock();
            }
        }
    }
}
