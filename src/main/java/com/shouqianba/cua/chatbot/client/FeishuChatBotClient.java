package com.shouqianba.cua.chatbot.client;

import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.chatbot.message.Message;
import com.shouqianba.cua.utils.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Map;

/**
 * 飞书自定义机器人客户端
 *
 * <AUTHOR>
 * @date 2024/8/6
 */
@Slf4j
public class FeishuChatBotClient implements ChatBotClient {

    private static final int CONNECT_TIMEOUT = 3000;
    private static final int READ_TIMEOUT = 5000;

    CloseableHttpClient httpclient;

    public FeishuChatBotClient() {
        this(CONNECT_TIMEOUT, READ_TIMEOUT);
    }

    public FeishuChatBotClient(int connectTimeout, int readTimeout) {
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(readTimeout)
                .setConnectTimeout(connectTimeout)
                .setConnectionRequestTimeout(connectTimeout)
                .build();
        httpclient = HttpClients.custom()
                .setMaxConnPerRoute(10)
                .setDefaultRequestConfig(requestConfig)
                .build();

    }

    @Override
    public SendResult send(String webhook, Message message, String secret) throws IOException {
        SendResult sendResult = new SendResult();
        if (webhook == null || webhook.isEmpty()) {
            sendResult.setErrorCode(null);
            sendResult.setErrorMsg("need webhook param");
            sendResult.setIsSuccess(false);
            return sendResult;
        }
        HttpPost httpPost = new HttpPost(webhook);
        httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
        Map<String, Object> body = message.toJsonMap();
        if (StringUtils.isNotBlank(secret)) {
            long timestamp = Instant.now().getEpochSecond();
            body.put("timestamp", timestamp);
            body.put("sign", genSign(secret, timestamp));
        }

        StringEntity se = new StringEntity(JSON.toJSONString(body), "utf-8");
        httpPost.setEntity(se);

        try (CloseableHttpResponse response = httpclient.execute(httpPost)) {
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String result = EntityUtils.toString(response.getEntity());
                JSONObject obj = JSONObject.parseObject(result);
                Integer errorCode = obj.getInteger("code");
                sendResult.setErrorCode(errorCode);
                sendResult.setErrorMsg(obj.getString("msg"));
                sendResult.setIsSuccess(errorCode != null && errorCode.equals(0));
            }
        }
        return sendResult;
    }

    private String genSign(String secret, long timestamp) {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        try {
            //使用HmacSHA256算法计算签名
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(new byte[]{});
            return new String(Base64.encodeBase64(signData));
        } catch (Exception e) {
            log.error("签名失败 {}", stringToSign, e);
        }
        return "";
    }
}
