package com.shouqianba.cua.chatbot.client;

import com.shouqianba.cua.chatbot.message.Message;

import java.io.IOException;

/**
 * 聊天机器人客户端
 *
 * <AUTHOR>
 * @date 2024/8/7
 */
public interface ChatBotClient {

    /**
     * 发送消息
     *
     * @param webhook 机器人webhook地址
     * @param message 推送到机器人的消息
     * @param secret  机器人安全秘钥（可为空）
     * @return
     * @throws IOException
     */
    SendResult send(String webhook, Message message, String secret) throws IOException;

}
