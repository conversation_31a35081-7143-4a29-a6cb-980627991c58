package com.shouqianba.cua.chatbot.message;

import com.wosai.common.utils.WosaiCollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.shouqianba.cua.enums.feishu.FeishuMemberEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/6
 */
public class TextMessage implements Message {

    private String text;

    /**
     * @ 所有人：必须满足所在群开启 @ 所有人功能。
     */
    private boolean isAtAll;

    /**
     * @ 指定群组成员
     */
    private List<FeishuMemberEnum> members;

    public TextMessage(String text) {
        this.text = text;
    }

    public TextMessage setAtAll(boolean atAll) {
        isAtAll = atAll;
        return this;
    }

    public TextMessage setAtMembers(List<FeishuMemberEnum> members) {
        this.members = members;
        return this;
    }

    public String getText() {
        return text;
    }

    public TextMessage setText(String text) {
        this.text = text;
        return this;
    }

    @Override
    public Map<String, Object> toJsonMap() {
        Map<String, Object> items = new HashMap<String, Object>();
        items.put("msg_type", "text");

        if (StringUtils.isBlank(text)) {
            throw new IllegalArgumentException("text should not be blank");
        }

        if (isAtAll) {
            text = String.format("%s <at user_id=\"all\">所有人</at>", text);
        } else if (WosaiCollectionUtils.isNotEmpty(members)) {
            String atMemberStr = getMemberStr();
            text = String.format("%s %s", text, atMemberStr);
        }

        Map<String, String> textContent = new HashMap<String, String>();
        textContent.put("text", text);
        items.put("content", textContent);
        return items;
    }

    private String getMemberStr() {
        StringBuilder atMemberBuilder = new StringBuilder();
        for (FeishuMemberEnum userEnum : members) {
            String userId = userEnum.getId();
            String name = userEnum.getName();
            atMemberBuilder.append("<at user_id=\""+userId+"\">"+name+"</at>");
        }
        return atMemberBuilder.toString();
    }

}
