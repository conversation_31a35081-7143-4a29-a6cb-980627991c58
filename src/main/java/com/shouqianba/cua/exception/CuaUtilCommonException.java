package com.shouqianba.cua.exception;

import com.wosai.common.exception.RuntimeWithCodeException;

/**
 * cua工具类运行时异常
 *
 * <AUTHOR>
 * @date 2024/3/29 10:43
 */
public class CuaUtilCommonException extends RuntimeWithCodeException {

    /**
     * 构造
     *
     * @param message 错误消息
     * @param cause   错误
     */
    public CuaUtilCommonException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造
     *
     * @param message 错误消息
     */
    public CuaUtilCommonException(String message) {
        super(message);
    }

    /**
     * 构造
     *
     * @param cause 错误
     */
    public CuaUtilCommonException(Throwable cause) {
        super(cause);
    }

    @Override
    public int getCode() {
        return 500;
    }
}
