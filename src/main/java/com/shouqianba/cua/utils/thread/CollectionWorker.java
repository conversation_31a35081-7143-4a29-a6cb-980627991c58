package com.shouqianba.cua.utils.thread;

import com.shouqianba.cua.exception.CuaUtilCommonException;
import com.wosai.middleware.hera.toolkit.trace.CallableWrapper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集合线程工作者 支持链路追踪
 *
 * @param <T> 泛型
 * <AUTHOR>
 */
public final class CollectionWorker<T> {

    private final Collection<T> collection;

    private ExecutorService executorService = ForkJoinPool.commonPool();

    private boolean parallel;

    private CollectionWorker(Collection<T> collection) {
        this.collection = new ArrayList<>(collection);
    }

    /**
     * 创建对象
     *
     * @param collection 集合
     * @param <T>        泛型
     * @return 对象
     */
    public static <T> CollectionWorker<T> of(Collection<T> collection) {
        return new CollectionWorker<>(collection);
    }

    /**
     * 开启并行
     *
     * @return 当前对象
     */
    public CollectionWorker<T> parallel() {
        this.parallel = true;
        return this;
    }

    /**
     * 开启并行
     *
     * @param executorService 执行器
     * @return 当前对象
     */
    public CollectionWorker<T> parallel(ExecutorService executorService) {
        this.parallel = true;
        this.executorService = executorService;
        return this;
    }

    /**
     * 是否开启并行
     *
     * @return true 开启， false 未开启
     */
    public boolean isParallel() {
        return parallel;
    }

    /**
     * 迭代
     *
     * @param action 操作
     */
    public void forEach(Consumer<? super T> action) {
        if (!parallel) {
            collection.forEach(action);
            return;
        }
        try {
            List<Callable<Void>> taskList = new ArrayList<>();
            for (T t : collection) {
                CallableWrapper<Void> callableWrapper = CallableWrapper.of(() -> {
                    action.accept(t);
                    return null;
                });
                taskList.add(callableWrapper);
            }
            invokeAll(taskList);
        } catch (Exception e) {
            throw new CuaUtilCommonException("forEach error", e);
        }
    }

    /**
     * 映射到列表
     *
     * @param mapper 映射
     * @param <R>    泛型
     * @return 映射列表
     */
    public <R> List<R> mapToList(Function<? super T, ? extends R> mapper) {
        if (!parallel) {
            return collection.stream().map(mapper).collect(Collectors.toList());
        }
        try {
            List<Callable<R>> taskList = new ArrayList<>();
            for (T t : collection) {
                taskList.add(CallableWrapper.of(() -> mapper.apply(t)));
            }
            return invokeAll(taskList);
        } catch (Exception e) {
            throw new CuaUtilCommonException("mapToListWithExecutor error", e);
        }
    }

    private <T> List<T> invokeAll(List<Callable<T>> taskList) throws InterruptedException, ExecutionException {
        List<Future<T>> futures = executorService.invokeAll(taskList);
        List<T> result = new ArrayList<>();
        for (Future<T> future : futures) {
            result.add(future.get());
        }
        return result;
    }
}
