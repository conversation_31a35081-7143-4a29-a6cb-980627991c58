package com.shouqianba.cua.utils.collection;

import com.google.common.collect.Iterators;

import java.util.Objects;
import java.util.Optional;

/**
 * 集合扩展工具
 *
 * <AUTHOR>
 */
public final class CollectionExtUtils {

    private CollectionExtUtils() {
        // hide construct
    }

    /**
     * 获取集合第一个
     *
     * @param items 集合
     * @param <T>   对象泛型
     * @return 集合第一个
     */
    public static <T> Optional<T> getFirst(Iterable<T> items) {

        // guava
        // Iterables.getFirst(, "default")
        if (Objects.isNull(items)) {
            return Optional.empty();
        }

        final T next = Iterators.getNext(items.iterator(), null);
        return Optional.ofNullable(next);
    }
}
