package com.shouqianba.cua.utils.object;


import com.shouqianba.cua.annotation.ITextValueEnum;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 枚举处理工具类
 *
 * <AUTHOR>
 * @date 2023/11/10
 */
public final class EnumUtils {

    private static final ConcurrentHashMap<Class<? extends ITextValueEnum<?>>, Map<Object, ? extends ITextValueEnum<?>>> ENUM_CLASS_TO_VALUE_CACHE_MAP = new ConcurrentHashMap<>();

    private static final Object LOCK = new Object();


    private EnumUtils() {
    }

    /**
     * 根据枚举值的内容返回枚举
     *
     * @param text       枚举值的内容
     * @param targetEnum 想要转化的枚举的类型
     * @return Optional<T>
     */
    @SuppressWarnings("squid:S1133")
    public static <T extends ITextValueEnum<?>> Optional<T> getEnumByTextValue(String text, Class<T> targetEnum) {
        for (int i = 0; i < targetEnum.getEnumConstants().length; i++) {
            if (targetEnum.getEnumConstants()[i].getText().equals(text)) {
                return Optional.of(targetEnum.getEnumConstants()[i]);
            }
        }
        return Optional.empty();
    }


    /**
     * 获取枚举，value可以为null，Optional结果可能是null
     *
     * @param targetEnum 想要转化的枚举的类型
     * @param value      value
     * @param <T>        枚举类型
     * @param <V>        枚举value类型
     * @return ITextValueEnum
     */
    public static <T extends ITextValueEnum<V>, V> Optional<T> ofNullable(@Nonnull Class<T> targetEnum, @Nullable V value) {
        return Optional.ofNullable(EnumUtils._getEnum(targetEnum, value));
    }


    /**
     * 获取枚举，value不可以为null，Optional结果不可能是null
     *
     * @param targetEnum 想要转化的枚举的类型
     * @param value      value
     * @param <T>        枚举类型
     * @param <V>        枚举value类型
     * @return ITextValueEnum
     */
    public static <T extends ITextValueEnum<V>, V> Optional<T> of(@Nonnull Class<T> targetEnum, @Nonnull V value) {
        return Optional.of(EnumUtils.getEnum(targetEnum, value));
    }

    /**
     * 获取枚举，value不可以为null，结果不可能是null
     *
     * @param targetEnum 想要转化的枚举的类型
     * @param value      value
     * @param <T>        枚举类型
     * @param <V>        枚举value类型
     * @return ITextValueEnum
     */
    @Nonnull
    public static <T extends ITextValueEnum<V>, V> T getEnum(@Nonnull Class<T> targetEnum, @Nonnull V value) {
        return Optional.ofNullable(EnumUtils._getEnum(targetEnum, value)).orElseThrow(() -> new IllegalArgumentException(
                "No enum constant found by value: " + value));
    }


    /**
     * 获取枚举值映射,如果不存在,则会自动初始化一次
     *
     * @param iTextValueEnumClazz 枚举class类
     * @param value               键值
     * @param <V>                 枚举value类型
     * @param <T>                 对应枚举类型
     * @return 对应枚举
     */
    @Nullable
    @SuppressWarnings({"squid:S2445", "squid:S00100", "unchecked"})
    private static <V, T extends ITextValueEnum<V>> T _getEnum(@Nonnull final Class<T> iTextValueEnumClazz, @Nullable final V value) {
        if (Objects.isNull(value)) {
            return null;
        }
        Map<Object, ? extends ITextValueEnum<?>> iTextValueEnumMappingMap = ENUM_CLASS_TO_VALUE_CACHE_MAP.get(iTextValueEnumClazz);
        if (Objects.nonNull(iTextValueEnumMappingMap)) {
            return (T) iTextValueEnumMappingMap.get(value);
        }
        synchronized (LOCK) {
            iTextValueEnumMappingMap = ENUM_CLASS_TO_VALUE_CACHE_MAP.get(iTextValueEnumClazz);
            if (Objects.nonNull(iTextValueEnumMappingMap)) {
                return (T) iTextValueEnumMappingMap.get(value);
            }
            iTextValueEnumMappingMap = Arrays.stream(iTextValueEnumClazz.getEnumConstants())
                    .collect(Collectors.toMap(ITextValueEnum::getValue, Function.identity()));
            ENUM_CLASS_TO_VALUE_CACHE_MAP.put(iTextValueEnumClazz, iTextValueEnumMappingMap);
            return (T) iTextValueEnumMappingMap.get(value);
        }
    }

    /**
     * 获取枚举类对应的所有的枚举
     *
     * @param iTextValueEnumClass 枚举类
     * @param <T>                 枚举类型
     * @return 枚举类对应的所有的枚举
     */
    public static  <T extends ITextValueEnum<V>, V> List<T> listAllEnums(Class<T> iTextValueEnumClass) {
        return Arrays.asList(iTextValueEnumClass.getEnumConstants());
    }
}
