package com.shouqianba.cua.utils.object;

import com.shouqianba.cua.exception.CuaUtilCommonException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 转换工具
 *
 * <AUTHOR>
 */
@Slf4j
public final class ConvertUtil {


    private ConvertUtil() {}

    /**
     * 强转为T类型的list
     *
     * @param result       待强转的数据
     * @param expectedType 转换的list元素类型
     * @param <T>          list元素类型
     * @return 强转后的元素为T类型的list
     */
    public static <T> List<T> castToExpectedList(Object result, Class<T> expectedType) {
        if (Objects.isNull(result)) {
            return Collections.emptyList();
        }
        if (result instanceof List<?>) {
            List<?> resultList = (List<?>) result;
            if (CollectionUtils.isEmpty(resultList)) {
                return Collections.emptyList();
            }
            if (expectedType.isInstance(resultList.get(0))) {
                return (List<T>) resultList;
            } else {
                throw new CuaUtilCommonException("Unexpected element type in the list");
            }
        }
        log.error("Unexpected result type: 原始类型:{}, 强转类型:{}", result.getClass(), expectedType);
        throw new CuaUtilCommonException("Unexpected result type");
    }

    /**
     * 强转为T类型
     *
     * @param result       待强转的数据
     * @param expectedType 预期转换的元素类型
     * @param <T>          预期元素类型
     * @return 转换后的数据
     */
    public static <T> Optional<T> castToExpectedType(Object result, Class<T> expectedType) {
        if (Objects.isNull(result)) {
            return Optional.empty();
        }
        if (expectedType.isInstance(result)) {
            return Optional.of(expectedType.cast(result));
        }
        log.error("Unexpected result type: 原始类型:{}, 强转类型:{}", result.getClass(), expectedType);
        throw new CuaUtilCommonException("Unexpected result type");
    }

}
