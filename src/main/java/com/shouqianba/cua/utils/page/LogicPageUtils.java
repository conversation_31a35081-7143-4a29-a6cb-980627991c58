package com.shouqianba.cua.utils.page;

import com.shouqianba.cua.model.page.NormalPagingResult;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 内存分页工具类
 *
 * <AUTHOR>
 */
public final class LogicPageUtils {

    private LogicPageUtils() {
    }

    /**
     * 获取某页数据
     *
     * @param pageNum 第几页
     * @param pageSize 每页条数
     * @param data 所有数据
     * @return 分页数据
     */
    public static <T> List<T> page(int pageNum, int pageSize, List<T> data) {
        if (CollectionUtils.isEmpty(data) || pageSize <= 0) {
            return Collections.emptyList();
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        int from = (pageNum - 1) * pageSize;
        int to = Math.min(pageNum * pageSize, data.size());
        if (from > to) {
            from = to;
        }
        return data.subList(from, to);
    }

    /**
     * 获取某页数据，并封装成 NormalPagingResult 对象
     *
     * @param pageNum  第几页
     * @param pageSize 每页条数
     * @param data     所有数据
     * @param <T>      数据类型
     * @return NormalPagingResult 对象
     */
    public static <T> NormalPagingResult<T> pageWithPagingResult(int pageNum, int pageSize, List<T> data) {
        NormalPagingResult<T> result = new NormalPagingResult<>();
        List<T> pageData = page(pageNum, pageSize, data);
        result.setList(pageData);
        result.setPageSize(pageSize);
        result.setPageNum(pageNum);
        result.setPages(getPageCount(pageSize, data));
        result.setTotal(data.size());
        result.setHasPreviousPage(pageNum > 1);
        result.setHasNextPage(pageNum < result.getPages());
        return result;
    }

    /**
     * 获取总页数
     *
     * @param pageSize 每页条数
     * @param data 所有数据
     * @return 总页数
     */
    public static <T> int getPageCount(int pageSize, List<T> data) {
        if (pageSize == 0 || CollectionUtils.isEmpty(data)) {
            return 0;
        }
        return (int) Math.ceil((double) data.size() / pageSize);
    }

}
