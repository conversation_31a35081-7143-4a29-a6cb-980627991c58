package com.shouqianba.cua.utils.identity;

import lombok.Data;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.format.ResolverStyle;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 证件有效期校验工具类
 * 根据规则校验证件的有效期是否合法
 */
public class IdentityValidityUtils {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("uuuuMMdd").withResolverStyle(ResolverStyle.STRICT);
    private static final String LONG_TERM_DATE = "99991231";
    private static final Pattern VALIDITY_PATTERN = Pattern.compile("\\d{8}-\\d{8}");

        /**
     * 校验证件有效期
     *
     * @param rule     校验规则，包含年份差、允许的月日偏移等配置信息
     * @param validity 有效期字符串，格式为 yyyyMMdd-yyyyMMdd；若为长期有效，则结束日期为 99991231
     * @return ValidationResult 校验结果对象，包含是否成功及失败原因
     */
    public static ValidationResult checkValidity(Rule rule, String validity) {
        // 参数校验
        Assert.notNull(rule, "规则不能为空");
        Assert.hasText(validity, "有效期不能为空");

        // 校验Rule中的字段均不能为空
        Assert.notNull(rule.getValidYearDiff(), "年差枚举不能为空");
        Assert.notNull(rule.getDayOffsetTolerance(), "允许的月日偏移不能为空");

        // 校验有效期格式是否符合 yyyyMMdd-yyyyMMdd 的要求
        if (!VALIDITY_PATTERN.matcher(validity).matches()) {
            return ValidationResult.failure("有效期格式不正确，应为yyyyMMdd-yyyyMMdd格式");
        }

        String[] parts = validity.split("-");
        String startDateStr = parts[0];
        String endDateStr = parts[1];

        LocalDate startDate;
        LocalDate endDate;

        try {
            startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
            endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            return ValidationResult.failure("日期错误");
        }

        // 1. 如果支持长期有效且结束日期为99991231，则直接返回校验成功
        if (rule.isSupportLongTerm() && LONG_TERM_DATE.equals(endDateStr)) {
            return ValidationResult.success();
        }

        // 2. 检查年份差是否在允许范围内
        if (rule.getValidYearDiff() != null && !rule.getValidYearDiff().isEmpty()) {
            int yearDiff = endDate.getYear() - startDate.getYear();
            // 年份差不符合要求
            if (!rule.getValidYearDiff().contains((int) yearDiff)) {
                return ValidationResult.failure("年份差错误");
            }
        }

        // 3. 检查有效期剩余天数是否满足最小要求
        LocalDate now = LocalDate.now();
        long remainingDays = ChronoUnit.DAYS.between(now, endDate);
        if (remainingDays < rule.getMinRemainingDays()) {
            return ValidationResult.failure(String.format("证件有效期需大于%d天", rule.getMinRemainingDays()));
        }

        // 4. 如果 MMdd-MMdd 范围存在于 uniqueValidity 中，则认为有效
        if (rule.getUniqueValidity() != null && !rule.getUniqueValidity().isEmpty()) {
            String mmddRange = startDateStr.substring(4) + "-" + endDateStr.substring(4);
            if (rule.getUniqueValidity().contains(mmddRange)) {
                return ValidationResult.success();
            }
        }

        // 5. 检查闰日特殊情况（优化后的逻辑）
        if (rule.isEnableLeapDayValidation() && isLeapDaySpecialCase(startDate, endDate)) {
            return ValidationResult.success();
        }

        // 6. 判断开始日期与结束日期的时间差是否超过允许的偏移量
        long daysBetween = Math.abs(ChronoUnit.DAYS.between(startDate.withYear(endDate.getYear()), endDate));
        if (daysBetween > rule.getDayOffsetTolerance()) {
            return ValidationResult.failure("开始日期和结束日期不匹配");
        }

        return ValidationResult.success();
    }

    /**
     * 检查是否为闰日特殊情况
     * 规则：
     * 1. 开始日期是 0228/0229，结束日期可以为 0227、0228、0229、0301、0302
     * 2. 结束日期是 0228/0229，开始日期可以为 0227、0228、0229、0301、0302
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 是否为闰日特殊情况
     */
    private static boolean isLeapDaySpecialCase(LocalDate startDate, LocalDate endDate) {
        String startMmdd = String.format("%02d%02d", startDate.getMonthValue(), startDate.getDayOfMonth());
        String endMmdd = String.format("%02d%02d", endDate.getMonthValue(), endDate.getDayOfMonth());

        // 定义闰日相关的特殊日期
        Set<String> leapDayDates = Set.of("0228", "0229");
        Set<String> leapDayToleranceDates = Set.of("0227", "0228", "0229", "0301", "0302");

        // 情况1：开始日期是闰日相关日期（0228/0229），检查结束日期是否在容忍范围内
        if (leapDayDates.contains(startMmdd) && leapDayToleranceDates.contains(endMmdd)) {
            return true;
        }

        // 情况2：结束日期是闰日相关日期（0228/0229），检查开始日期是否在容忍范围内
        if (leapDayDates.contains(endMmdd) && leapDayToleranceDates.contains(startMmdd)) {
            return true;
        }

        return false;
    }


    @Data
    public static class Rule {
        // 年差枚举：5、10、20
        Set<Integer> validYearDiff;
        // 允许的月日偏移：0 同一天，±1 前后一天
        Integer dayOffsetTolerance;
        // 是否支持长期有效
        boolean supportLongTerm;
        // 至少剩余天数
        int minRemainingDays;
        // 特殊有效期 如（0228-0229）- 保留用于其他特殊情况
        Set<String> uniqueValidity;
        // 是否启用闰日校验逻辑
        boolean enableLeapDayValidation = false;
    }

    @Data
    public static class ValidationResult {
        /**
         * 是否校验通过
         */
        private boolean valid;

        /**
         * 校验失败原因
         */
        private String reason;

        /**
         * 创建校验通过的结果
         *
         * @return ValidationResult
         */
        public static ValidationResult success() {
            ValidationResult result = new ValidationResult();
            result.setValid(true);
            return result;
        }

        /**
         * 创建校验失败的结果
         *
         * @param reason 失败原因
         * @return ValidationResult
         */
        public static ValidationResult failure(String reason) {
            ValidationResult result = new ValidationResult();
            result.setValid(false);
            result.setReason(reason);
            return result;
        }
    }
}