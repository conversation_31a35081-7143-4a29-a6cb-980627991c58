package com.shouqianba.cua.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 脱敏字段
 * 该注解必须配合DesensitizationEntityId注解使用(当前类必须有字段被EncryptEntityId注解标注),用来标识脱敏实体类型
 *
 * <AUTHOR>
 * @date 2024/4/26 15:11
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DesensitizationField {
}
