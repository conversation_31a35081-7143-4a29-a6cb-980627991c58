package com.shouqianba.cua.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 脱敏实体id
 *
 *
 * <AUTHOR>
 * @date 2024/4/26 15:11
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DesensitizationEntityId {

    /**
     * 脱敏实体类型 参考DesensitizationEntityEnum
     *
     * @return 加密实体类型
     */
    String type();
}
