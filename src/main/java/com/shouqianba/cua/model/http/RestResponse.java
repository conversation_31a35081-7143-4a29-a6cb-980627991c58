package com.shouqianba.cua.model.http;


import lombok.Getter;

import java.io.Serializable;


/**
 * http 通用返回
 *
 * @param <T> 实体
 * <AUTHOR>
 * @date 2024/3/28 10:11
 */

public class RestResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private static final Integer CODE_SUCCESS = 200;
    private static final Integer CODE_FAILURE = -1;
    private static final String MSG_SUCCESS = "success";
    private static final String MSG_FAILURE = "failure";

    /**
     * 状态码
     */
    @Getter
    private Integer code;

    /**
     * 消息
     */
    private String msg;

    /**
     * 数据
     */
    @Getter
    private T data;

    public RestResponse() {
    }

    public RestResponse(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> RestResponse<T> Success(T data) {
        return new RestResponse<>(CODE_SUCCESS, MSG_SUCCESS, data);
    }

    public static <T> RestResponse<T> Success(String message, T data) {
        return new RestResponse<>(CODE_SUCCESS, message, data);
    }

    public static <T> RestResponse<T> Fail(T data) {
        return new RestResponse<>(CODE_FAILURE, MSG_FAILURE, data);
    }

    public static <T> RestResponse<T> Fail(String message, T data) {
        return new RestResponse<>(CODE_FAILURE, message, data);
    }

    public static <T> RestResponse<T> Fail(Integer code, String message,  T data) {
        return new RestResponse<>(code, message, data);
    }

    public String getMessage() {
        return this.msg;
    }

}

