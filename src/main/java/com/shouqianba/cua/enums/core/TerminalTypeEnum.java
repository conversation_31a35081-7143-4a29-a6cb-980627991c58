package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;


/**
 * 终端类型枚举
 *
 * <AUTHOR>
 */
public enum TerminalTypeEnum implements ITextValueEnum<Integer> {

    /**
     * Android应用
     */
    ANDROID_APP(10, "Android应用"),

    /**
     * iOS应用
     */
    IOS_APP(11, "iOS应用"),

    /**
     * Windows桌面应用
     */
    WINDOWS_DESKTOP_APP(20, "Windows桌面应用"),

    /**
     * 专用设备
     */
    SPECIAL_DEVICE(30, "专用设备"),

    /**
     * 门店码
     */
    STORE_CODE(40, "门店码"),

    /**
     * 服务
     */
    SERVICE(50, "服务");

    TerminalTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }
    private final int value;
    private final String text;


    public String getText() {
        return this.text;
    }

    public Integer getValue() {
        return this.value;
    }
}
