package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 收单机构机构类型枚举
 *
 * <AUTHOR>
 */
public enum AcquirerOrgTypeEnum implements ITextValueEnum<Integer> {

    /**
     * 三方机构
     */
    THIRD_PARTY(1, "三方机构"),

    /**
     * 银行机构
     */
    BANK(2, "银行机构"),

    /**
     * 支付源直连机构
     */
    PAY_SOURCE_DIRECT(3, "支付源直连机构");


    private final int value;
    private final String text;

    AcquirerOrgTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
