package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;

import java.util.Objects;

/**
 * 营业执照更新场景下，结算账户类型枚举
 *
 * <AUTHOR>
 */
public enum LicenseSettlementAccountTypeEnum implements ITextValueEnum<Integer> {

    LEGAL_PERSONAL(1, "法人代表个人账户"),

    CORPORATE(2, "对公企业账户"),

    OTHER_PERSONAL(3, "其他个人账户"),

    AUTHORIZED_CORPORATE(4, "授权对公企业账户");

    private final int value;
    private final String text;

    LicenseSettlementAccountTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 是否是对私账户
     * @param value licenseSettlementAccountType
     * @return bankAccountType
     */
    public static boolean isPersonal(Integer value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value == LEGAL_PERSONAL.value || value == OTHER_PERSONAL.value;
    }

    /**
     * 是否是对公账户
     * @param value licenseSettlementAccountType
     * @return bankAccountType
     */
    public static boolean isPublic(Integer value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value == CORPORATE.value || value == AUTHORIZED_CORPORATE.value;
    }

    /**
     * 根据 value 查询对应枚举
     * @param value 结算账户类型value值
     * @return 结算账户枚举
     */
    public static LicenseSettlementAccountTypeEnum getEnumByValue(Integer value) {
        if (Objects.nonNull(value)) {
            for (LicenseSettlementAccountTypeEnum licenseSettlementAccountTypeEnum : LicenseSettlementAccountTypeEnum.values()) {
                if (licenseSettlementAccountTypeEnum.value == value) {
                    return licenseSettlementAccountTypeEnum;
                }
            }
        }
        return null;
    }
}
