package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;


/**
 * 支付源枚举
 *
 * <AUTHOR>
 */
public enum PaywayEnum implements ITextValueEnum<Integer> {

    /**
     * 收单机构
     */
    ACQUIRER(0, "收单机构"),

    /**
     * 支付宝1.0
     */
    ALIPAY_V1(1, "支付宝1.0"),

    /**
     * 支付宝
     */
    ALIPAY(2, "支付宝2.0"),

    /**
     * 微信
     */
    WEIXIN(3, "微信"),
    /**
     * 百付宝
     */
    BAIFUBAO(4, "百付宝"),
    /**
     * 京东钱包
     */
    JD_WALLET(5, "京东钱包"),
    /**
     * qq钱包
     */
    QQ_WALLET(6, "QQ钱包"),
    /**
     * 苹果支付
     */
    APPLEPAY(7, "苹果支付"),
    /**
     * 拉卡拉钱包
     */
    LAKALA_WALLET(8, "拉卡拉钱包"),
    /**
     * 和支付
     */
    CMCC(9, "和支付"),

    /**
     * 云闪付
     */
    UNIONPAY(17, "云闪付"),

    /**
     * 翼支付
     */
    BESTPAY(18, "翼支付"),

    /**
     * 微信香港本地支付
     */
    WEIXIN_HK(19, "微信香港本地支付"),
    /**
     * 支付宝国际版
     */
    ALIPAY_INTL(20, "支付宝国际版"),

    /**
     * 银行卡刷卡
     */
    BANK_CARD(21, "银行卡刷卡"),

    /**
     * 数字人民币
     */
    DCEP(23, "数字人民币"),

    /**
     * 饭卡
     */
    FOOD_CARD(31, "饭卡"),

    /**
     * 新希望集团院校通产品：一码通
     */
    YMT(32, "一码通"),
    ;

    PaywayEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    private final int value;
    private final String text;


    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }

    public String getValueAsString() {
        return String.valueOf(this.value);
    }
}
