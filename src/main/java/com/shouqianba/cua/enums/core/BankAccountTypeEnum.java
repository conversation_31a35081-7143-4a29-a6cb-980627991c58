package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;
import com.shouqianba.cua.utils.object.EnumUtils;

import java.util.Objects;

/**
 * 银行账户类型
 *
 * <AUTHOR>
 * @date 2023/12/1
 */
public enum BankAccountTypeEnum implements ITextValueEnum<Integer> {

    /**
     * 对私银行卡
     */
    PERSONAL(1, "对私银行卡"),

    /**
     * 对公账户
     */
    PUBLIC(2, "对公账户");


    private final int value;

    private final String text;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }

    BankAccountTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    public static BankAccountTypeEnum of(int value) {
        return EnumUtils.getEnum(BankAccountTypeEnum.class, value);
    }

    /**
     * 是否对私银行卡
     *
     * @param value
     * @return
     */
    public static boolean isPersonal(Integer value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value == PERSONAL.value;
    }

    /**
     * 是否对公账户
     *
     * @param value
     * @return
     */
    public static boolean isPublic(Integer value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value == PUBLIC.value;
    }

    /**
     * 是否对私银行卡
     *
     * @return
     */
    public boolean isPersonal() {
        return value == PERSONAL.value;
    }

    /**
     * 是否对公账户
     *
     * @return
     */
    public boolean isPublic() {
        return value == PUBLIC.value;
    }

    /**
     * 根据value查询对应枚举
     * @param value 账户类型value值
     * @return 账户类型枚举
     */
    public static BankAccountTypeEnum getEnumByValue(Integer value) {
        if (Objects.nonNull(value)) {
            for (BankAccountTypeEnum bankAccountTypeEnum : BankAccountTypeEnum.values()) {
                if (bankAccountTypeEnum.value == value) {
                    return bankAccountTypeEnum;
                }
            }
        }
        return null;
    }
}
