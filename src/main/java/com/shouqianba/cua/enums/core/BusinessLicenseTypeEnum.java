package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;
import com.shouqianba.cua.utils.object.EnumUtils;

import java.util.Objects;

/**
 * 营业执照类型枚举
 *
 * <AUTHOR>
 * @date 2023/12/6
 */
public enum BusinessLicenseTypeEnum implements ITextValueEnum<Integer> {
    /**
     * 小微
     */
    MICRO(0, "小微"),

    /**
     * 个体
     */
    INDIVIDUAL(1, "个体营业执照"),

    /**
     * 企业
     */
    ENTERPRISE(2, "企业营业执照"),

    /**
     * 组织类营业执照，>=3 都归属这类
     */
    INSTITUTIONAL(3, "事业单位法人证书"),
    NO_ENTERPRISE(4, "民办非企业单位登记证书"),
    COMMUNITY(5, "社会团体法人登记证书"),
    FOUNDATION(6, "基金会法人登记证书"),
    LAW_FIRM(7, "律师事务所执业许可证"),
    RELIGION(8, "宗教活动场所法人登记证书"),
    COOPERATIVE(9, "农民专业合作社"),
    WELFARE(10, "社会福利机构设置批准证书"),
    UNIFORM(11, "统一社会信用代码证书"),
    COLLECTIVE_VILLAGE(12, "农村集体经济组织登记证（村级）"),
    JUDICIARY(13, "司法鉴定许可证"),
    UNFIED_SOICAL_COMMITTEE(14, "基层群众性自治组织特别法人统一社会信用代码证书（居委会）"),
    UNFIED_SOCIAL_VILLAGE(16, "基层群众性自治组织特别法人统一社会信用代码证书（村委会）"),
    RURAL_SUIT(17, "农村集体经济组织登记证（组级）"),
    RURAL_TOWN(18, "农村集体经济组织登记证（乡镇级）"),
    RURAL_OTHER(19, "农村集体经济组织登记证（其他）"),
    PROPRIETORS(20, "业主大会统一社会信用代码证书"),
    BASIC_LEGAL_SERVICE(21, "基层法律服务所执业证"),
    MEDICAL(22, "医疗机构执业许可证"),
    TRADE_UNION(23, "工会法人资格证书"),
    FOREIGN_FUNDED_ENTERPRISES(24, "外商投资企业批准证书"),
    SOCIAL_SERVICE_AGENCY(25, "社会服务机构登记证书"),
    RELIGIOUS_ACTIVITIES(26, "宗教活动场所登记证"),
    OWENERS_COMMITTEE_RECORD_CERTIFICATE(27, "业主委员会备案证明"),
    EXEQUATUR(28, "领事证书"),
    UNIFORM1(29, "统一社会信用代码证书（1）"),
    OWNERS_COMMITTEE_RECORD_CERTIFICATE1(30, "业主委员会备案证明（1）"),
    OWNERS_COMMITTEE_RECORD_CERTIFICATE2(31, "业主委员会备案证明（2）");

    private int value;

    private String text;

    BusinessLicenseTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }

    public static BankAccountTypeEnum of(int value) {
        return EnumUtils.getEnum(BankAccountTypeEnum.class, value);
    }

    /**
     * 是否小微
     *
     * @return
     */
    public boolean isMicro() {
        return value == MICRO.value;
    }

    /**
     * 是否个体户
     *
     * @return
     */
    public boolean isIndividual() {
        return value == INDIVIDUAL.value;
    }

    /**
     * 是否企业户
     *
     * @return
     */
    public boolean isEnterprise() {
        return value == ENTERPRISE.value;
    }

    /**
     * 是否组织户
     *
     * @return
     */
    public boolean isOrganization() {
        return value > ENTERPRISE.value;
    }

    /**
     * 是否小微
     *
     * @param value
     * @return
     */
    public static boolean isMicro(Integer value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value == MICRO.value;
    }

    /**
     * 是否个体户
     *
     * @param value
     * @return
     */
    public static boolean isIndividual(Integer value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value == INDIVIDUAL.value;
    }

    /**
     * 是否企业户
     *
     * @param value
     * @return
     */
    public static boolean isEnterprise(Integer value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value == ENTERPRISE.value;
    }

    /**
     * 是否组织户
     *
     * @param value
     * @return
     */
    public static boolean isOrganization(Integer value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value > ENTERPRISE.value;
    }

    public static BusinessLicenseTypeEnum valueOf(int type) {
        for (BusinessLicenseTypeEnum value : BusinessLicenseTypeEnum.values()) {
            if (type == value.getValue()) {
                return value;
            }
        }
        return null;
    }


}
