package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * <AUTHOR>
 */
public enum IdentificationTypeEnum implements ITextValueEnum<Integer> {
    PRC_ID_CARD(1, "中华人民共和国身份证"),
    HK_MACAO_PERMIT(2, "港澳居民往来内地通行证"),
    TAIWAN_PERMIT(3, "台湾居民往来内地通行证"),
    NON_PRC_PASSPORT(4, "非中华人民共和国护照"),
    PRC_PASSPORT(5, "中华人民共和国护照"),
    HK_MACAO_RESIDENCE_PERMIT(6, "港澳居民居住证"),
    TAIWAN_RESIDENCE_PERMIT(7, "台湾居民居住证"),
    EXECUTIVE_PARTNER(8, "执行事务合伙人"),
    FOREIGNER_PERMANENT_RESIDENCE_ID_CARD(9, "外国人永久居留身份证");

    private Integer value;
    private String text;

    IdentificationTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}