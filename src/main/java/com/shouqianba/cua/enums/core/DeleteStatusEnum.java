package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;


/**
 * 删除状态枚举
 *
 * <AUTHOR>
 */
public enum DeleteStatusEnum implements ITextValueEnum<Integer> {
    /**
     * 未删除
     */
    NO_DELETED(0, "未删除"),
    /**
     * 已删除
     */
    DELETED(1, "已删除");

    private final int value;
    private final String text;

    DeleteStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
