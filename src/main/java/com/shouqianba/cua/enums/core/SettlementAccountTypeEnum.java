package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 结算账户类型枚举
 *
 * <AUTHOR>
 */
public enum SettlementAccountTypeEnum implements ITextValueEnum<Integer> {

    /**
     * 小微对私
     */
    SMALL_MICRO_PRIVATE(1, "小微对私"),

    /**
     * 个体法人对私
     */
    INDIVIDUAL_PRIVATE(2, "个体法人对私"),

    /**
     * 个体非法人对私
     */
    INDIVIDUAL_UNREGISTERED_PRIVATE(4, "个体非法人对私"),

    /**
     * 个人对公
     */
    INDIVIDUAL_PUBLIC(3, "个人对公"),

    /**
     * 企业/组织法人对私
     */
    ORGANIZATION_PRIVATE(5, "企业/组织法人对私"),

    /**
     * 企业/组织非法人对私
     */
    ORGANIZATION_UNREGISTERED_PRIVATE(7, "企业/组织非法人对私"),

    /**
     * 企业/组织对公
     */
    ORGANIZATION_PUBLIC(6, "企业/组织对公");


    private final int value;
    private final String text;

    SettlementAccountTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
