package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 清算类型枚举
 *
 * <AUTHOR>
 */
public enum ClearTypeEnum implements ITextValueEnum<Integer> {

    /**
     * 直清
     */
    DIRECT(2, "直清"),

    /**
     * 间清
     */
    INDIRECT(1, "间清");

    private final int value;
    private final String text;

    ClearTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
