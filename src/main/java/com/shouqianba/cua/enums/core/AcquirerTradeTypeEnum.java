package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 收单机构交易类型枚举
 *
 * <AUTHOR>
 */
public enum AcquirerTradeTypeEnum implements ITextValueEnum<Integer> {

    /**
     * 间连
     */
    INDIRECT(1, "间连"),

    /**
     * 直连
     */
    DIRECT(2, "直连");


    private final int value;
    private final String text;

    AcquirerTradeTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
