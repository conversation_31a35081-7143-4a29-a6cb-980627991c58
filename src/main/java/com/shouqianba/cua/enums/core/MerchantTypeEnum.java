package com.shouqianba.cua.enums.core;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 商户类型枚举
 *
 * <AUTHOR>
 */
public enum MerchantTypeEnum implements ITextValueEnum<Integer> {

    /**
     * 小微商户
     */
    SMALL_MICRO_MERCHANT(1, "小微"),

    /**
     * 个体户商户
     */
    INDIVIDUAL_MERCHANT(2, "个体户"),

    /**
     * 企业商户
     */
    COMPANY_MERCHANT(3, "企业"),

    /**
     * 组织商户
     */
    ORGANIZATION_MERCHANT(4, "组织");

    private final int value;
    private final String text;

    MerchantTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}