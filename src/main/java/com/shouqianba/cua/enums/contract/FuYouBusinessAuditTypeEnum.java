package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 富友业务审核类型枚举
 *
 * <AUTHOR>
 */
public enum FuYouBusinessAuditTypeEnum implements ITextValueEnum<String> {

    /**
     * D0 业务
     */
    DAY_ZERO("TZ", "D0 业务"),

    /**
     * 银联二维码
     */
    UNION_PAY_QR_CODE("YL", "银联二维码"),

    /**
     * 分账
     */
    SETTLEMENT("LA", "分账"),

    /**
     * 扫码预授权
     */
    SCAN_CODE_PRE_AUTHORIZATION("PA", "扫码预授权"),

    /**
     * 红火计划类型
     */
    RED_FIRE_PLAN_TYPE("HH", "红火计划类型"),

    /**
     * 入账信息
     */
    ENTRY_INFORMATION("RZ", "入账信息"),

    /**
     * 商户基本信息变更
     */
    MERCHANT_BASIC_INFORMATION_CHANGE("BS", "商户基本信息变更"),

    /**
     * 结算方式变更
     */
    SETTLEMENT_METHOD_CHANGE("JS", "结算方式变更"),

    /**
     * 扫码常规变更
     */
    SCAN_CODE_GENERAL_CHANGE("SM", "扫码常规变更"),

    /**
     * 收单常规变更
     */
    ACQUIRING_GENERAL_CHANGE("SD", "收单常规变更"),

    /**
     * 支付宝特殊费率
     */
    ALIPAY_SPECIAL_RATE("A2", "支付宝特殊费率"),

    /**
     * 微信特殊渠道申请
     */
    WECHAT_SPECIAL_CHANNEL_APPLICATION("SP", "微信特殊渠道申请"),

    /**
     * 新增终端
     */
    ADD_TERMINAL("XZ", "新增终端"),

    /**
     * 终端替换
     */
    TERMINAL_REPLACE("ZD", "终端替换"),

    /**
     * 终端撤销
     */
    TERMINAL_REVOCATION("CJ", "终端撤销"),

    /**
     * 外卡开通
     */
    FOREIGN_CARD_OPEN("WK", "外卡开通"),

    /**
     * 合并结算
     */
    MERGE_SETTLEMENT("CS", "合并结算");


    private final String value;
    private final String text;

    FuYouBusinessAuditTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
