package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 是否支持特殊行业
 * <AUTHOR>
 * @date 2025/07/16
 */
public enum SupportSpecialIndustryTypeEnum implements ITextValueEnum<Integer> {
    YES(1, "支持特殊行业"),
    NO(0, "不支持特殊行业");


    private final int value;
    private final String text;

    SupportSpecialIndustryTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
