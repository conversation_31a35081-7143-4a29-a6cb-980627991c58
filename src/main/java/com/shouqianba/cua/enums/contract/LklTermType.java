package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * @Description: 拉卡拉终端交易上送类型
 * <AUTHOR>
 * @Date 2024/4/12 09:35
 */
public enum LklTermType implements ITextValueEnum<String> {
    TYPE_04("04","简易智能POS"),
    ;
    private final String value;
    private final String text;

    LklTermType(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public String getText() {
        return text;
    }
}
