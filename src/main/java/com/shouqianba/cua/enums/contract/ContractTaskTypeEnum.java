package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;


/**
 * 进件任务类型枚举
 *
 * <AUTHOR>
 */
public enum ContractTaskTypeEnum implements ITextValueEnum<String> {

    /**
     * 增网增终
     */
    INCREASE_NETWORK_TERMINAL("增网增终", ""),
    /**
     * 新增商户入网
     */
    NEW_MERCHANT_ONLINE("新增商户入网", ""),
    /**
     * 重新报备
     */
    RE_REPORT("重新报备", ""),
    /**
     * 微信商家认证
     */
    WECHAT_BUSINESS_CERTIFICATION("微信商家认证", ""),
    /**
     * 微信商户池报备
     */
    WECHAT_MERCHANT_POOL_REPORT("微信商户池报备", ""),
    /**
     * 支付宝直连
     */
    ALIPAY_DIRECT_CONNECTION("支付宝直连", ""),
    /**
     * 更新商户基本信息至拉卡拉
     */
    UPDATE_MERCHANT_BASIC_INFO_TO_LAKALA("更新商户基本信息至拉卡拉", ""),
    /**
     * 结算账户变更
     */
    SETTLEMENT_ACCOUNT_CHANGE("结算账户变更", ""),
    /**
     * 支付宝商家认证
     */
    ALIPAY_BUSINESS_CERTIFICATION("支付宝商家认证", ""),
    /**
     * 更新商户基本信息
     */
    UPDATE_MERCHANT_BASIC_INFO("更新商户基本信息", ""),
    /**
     * 更新商户费率
     */
    UPDATE_MERCHANT_RATE("更新商户费率", ""),
    /**
     * 更新营业执照
     */
    UPDATE_BUSINESS_LICENSE("更新营业执照", ""),
    /**
     * 微信直连线下
     */
    WECHAT_DIRECT_CONNECTION_OFFLINE("微信直连线下", ""),
    /**
     * 批量任务生成
     */
    BATCH_TASK_GENERATION("批量任务生成", ""),
    /**
     * 更新商户费率至拉卡拉
     */
    UPDATE_MERCHANT_RATE_TO_LKL("更新商户费率至拉卡拉", ""),
    /**
     * 附件上传
     */
    ATTACHMENT_UPLOAD("附件上传", ""),
    /**
     * 开通D0业务
     */
    OPEN_DAY_ZERO("开通D0业务", ""),
    /**
     * 小微升级
     */
    MICRO_UPGRADE("小微升级", "");

    ContractTaskTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }
    private final String value;
    private final String text;


    public String getText() {
        return this.text;
    }

    public String getValue() {
        return this.value;
    }
}
