package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 收单机构商户状态
 *
 * <AUTHOR>
 * @date 2024/9/4 16:38
 */
public enum AcquirerMerchantStatusEnum implements ITextValueEnum<Integer> {

    NORMAL(1, "正常"),

    DISABLE(2, "禁用"),

    CLOSE(3, "关闭");


    private final Integer value;
    private final String text;

    AcquirerMerchantStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
