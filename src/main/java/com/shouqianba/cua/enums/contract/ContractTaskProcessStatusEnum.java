package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;
import lombok.Getter;


/**
 * 任务处理状态枚举
 *
 * <AUTHOR>
 */
public enum ContractTaskProcessStatusEnum implements ITextValueEnum<Integer> {


    /**
     * 待处理
     */
    WAIT_PROCESS(0, "待处理"),

    /**
     * 处理中
     */
    BEING_PROCESSING(1, "处理中"),

    /**
     * 等待授权
     */
    WAIT_FOR_AUTH(2, "等待授权"),

    /**
     * 代付校验中
     */
    PAYMENT_VERIFICATION(3, "代付校验中"),

    /**
     * 处理成功
     */
    PROCESS_SUCCESS(5, "处理成功"),

    /**
     * 处理失败
     */
    PROCESS_FAIL(6, "处理失败"),

    /**
     * 审核中
     */
    AUDITING(99, "审核中");

    ContractTaskProcessStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }
    private final int value;

    @Getter
    private final String text;


    public Integer getValue() {
        return this.value;
    }
}
