package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * @Description: 华夏终端交易上送类型
 * <AUTHOR>
 * @Date 2024/4/12 09:35
 */
public enum HxbTermType implements ITextValueEnum<String> {
    TYPE_10("10","联迪QM50"),
    ;
    private final String value;
    private final String text;

    HxbTermType(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public String getText() {
        return text;
    }
}
