package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * @Description: 富友银行卡刷卡设备终端交易上送类型
 * <AUTHOR>
 * @Date 2024/4/12 09:35
 */
public enum FuYouTermType implements ITextValueEnum<String> {
    TYPE_04("04","富友银行卡刷卡设备"),
    ;
    private final String value;
    private final String text;

    FuYouTermType(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public String getText() {
        return text;
    }
}
