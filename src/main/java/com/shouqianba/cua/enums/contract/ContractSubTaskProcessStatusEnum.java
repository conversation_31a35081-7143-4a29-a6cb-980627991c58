package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;
import lombok.Getter;


/**
 * 子任务处理状态枚举
 *
 * <AUTHOR>
 */
public enum ContractSubTaskProcessStatusEnum implements ITextValueEnum<Integer> {



    /**
     * 待处理
     */
    WAIT_PROCESS(0, "待处理"),

    /**
     * 处理中
     */
    BEING_PROCESSING(1, "处理中"),


    //3

    /**
     * 系统异常失败
     */
    SYSTEM_EXCEPTION_FAILURE(4, "系统异常失败"),

    /**
     * 处理成功
     */
    PROCESS_SUCCESS(5, "处理成功"),

    /**
     * 处理失败
     */
    PROCESS_FAIL(6, "处理失败"),

    /**
     * 等待回调
     */
    WAIT_CALL_BACK_TEN(10, "等待回调"),

    /**
     * 复议中
     */
    RECONSIDERATION(11, "复议中"),

    /**
     * 进件换卡失败代付中
     */
    CONTRACT_CHANGE_CARD_FAILURE_PAYMENT(12, "进件换卡失败代付中"),

    /**
     * 等待回调
     */
    WAIT_CALL_BACK(99, "等待回调");

    ContractSubTaskProcessStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }
    private final int value;

    @Getter
    private final String text;


    public Integer getValue() {
        return this.value;
    }
}
