package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;



/**
 * 进件子任务类型枚举
 *
 * <AUTHOR>
 */
public enum ContractSubTaskTypeEnum implements ITextValueEnum<Integer> {

    /**
     * 基本信息变更
     */
    BASIC_INFO_CHANGE(0, "基本信息变更"),

    /**
     * 商户状态
     */
    MERCHANT_STATUS_CHANGE(1, "商户状态"),

    /**
     * 结算账户变更
     */
    SETTLEMENT_ACCOUNT_CHANGE(2, "结算账户变更"),

    /**
     * 费率更新
     */
    FEE_UPDATE(3, "费率更新"),

    /**
     * 银行卡更新
     */
    BANK_CARD_UPDATE(4, "银行卡更新"),

    /**
     * 进件
     */
    MERCHANT_OPENING(5, "进件"),

    /**
     * 更新
     */
    UPDATE(6, "更新"),


    /**
     * 新增门店
     */
    ADD_STORE(10, "新增门店"),

    /**
     * 新增终端
     */
    ADD_TERMINAL(11, "新增终端"),

    /**
     * 解绑终端
     */
    UNBIND_TERMINAL(12, "解绑终端"),

    /**
     * 营业执照变更
     */
    BUSINESS_LICENSE_CHANGE(13, "营业执照变更"),

    /**
     * 银联商户报备查询
     */
    UNION_PAY_MERCHANT_REPORT_QUERY(14, "银联商户报备查询"),

    /**
     * 开通D0业务
     */
    OPEN_DAY_ZERO(15, "开通D0业务"),

    /**
     * D0业务开通结果查询
     */
    QUERY_DAY_ZERO_OPEN_RESULT(16, "查询D0业务开通结果"),

    /**
     * 附件上传
     */
    ATTACHMENT_UPLOAD(99, "附件上传"),

    /**
     * 小微升级重新入网
     */
    MICRO_MERCHANT_UPGRADE_RE_ENTRY(20, "小微升级重新入网"),

    /**
     * 小微升级切换参数
     */
    MICRO_MERCHANT_UPGRADE_SWITCH_PARAM(21, "小微升级切换参数"),

    /**
     * 结算账户代付校验
     */
    PAYMENT_VERIFICATION(22, "结算账户代付校验"),

    STORE_PHOTO_UPDATE(23, "商户门头照更新"),
    /**
     * 小微升级切换参数
     */
    MICRO_MERCHANT_UPGRADE_PAY_AUTH(24, "小微升级支付源认证"),



    ;


    ContractSubTaskTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }
    private final int value;
    private final String text;


    public String getText() {
        return this.text;
    }

    public Integer getValue() {
        return this.value;
    }
}
