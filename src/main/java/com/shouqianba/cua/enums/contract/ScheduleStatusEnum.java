package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 调度状态枚举
 *
 * <AUTHOR>
 */
public enum ScheduleStatusEnum implements ITextValueEnum<Integer> {

    NOT_CAN(0, "不可调度"),

    CAN(1, "可调度");

    private final Integer value;
    private final String text;

    ScheduleStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
