package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 费率生效类型
 * <AUTHOR>
 * @date 2024/9/18
 */
public enum FeeEffectiveTypeEnum implements ITextValueEnum<Integer> {

    /**
     * 收钱吧
     */
    SQB(1, "以收钱吧为准"),

    /**
     * 收单机构
     */
    ACQUIRER(2, "以收单机构为准");


    private final int value;
    private final String text;

    FeeEffectiveTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
