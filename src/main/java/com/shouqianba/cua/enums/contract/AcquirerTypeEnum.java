package com.shouqianba.cua.enums.contract;


import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 收单机构类型枚举
 *
 * <AUTHOR>
 */
public enum AcquirerTypeEnum implements ITextValueEnum<String> {

    LKL("lkl", "拉卡拉"),
    ALI_PAY("alipay", "支付宝"),
    WEI_XIN("weixin", "微信"),
    BEST_PAY("bestpay", "翼支付"),
    CIB("cib", "上海兴业"),
    TONG_LIAN("tonglian", "通联"),
    UMS("ums", "银联商务"),
    PSBC("psbc", "邮储银行"),
    LKL_V3("lklV3", "拉卡拉V3"),
    CGB("cgb", "广发银行"),
    CCB("ccb", "建设银行"),
    HXB("hxb", "华夏银行"),
    LZB("lzb", "泸州银行"),
    ICBC("icbc", "工商银行"),
    TONG_LIAN_V2("tonglianV2", "通联收银宝"),
    HAI_KE("haike", "海科"),
    FU_YOU("fuyou", "富友"),
    PAB("pab","平安银行"),
    ZJTLCB("zjtlcb", "浙江泰隆银行"),
    FJNX("fjnx", "福建农信"),
    JSB("jsb", "江苏银行"),
    CMBC("cmbc", "民生银行"),
    GUOTONG("guotong", "国通星驿"),
    UMB("umb", "中投科信"),
    YXT("yxt", "院校通"),
    PSBCSX("psbcsx", "邮储银行山西分行"),
    BCS("bcs", "长沙银行"),
    ;

    AcquirerTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }
    private final String value;

    private final String text;


    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
