package com.shouqianba.cua.enums.contract;


/**
 * 华夏报备通道枚举
 *
 * <AUTHOR>
 * @date 2023/11/16 10:19
 */
public enum HXChannelEnum {

    /**
     * 华夏银行
     */
    HX_BANK(1, "hxb"),

    /**
     * 华夏银行-支付宝
     */
    HX_ALI(2, "hxb-1028-2"),

    /**
     * 华夏银行-微信
     */
    HX_WX(3, "hxb-1028-3"),

    /**
     * 华夏银行-银联云闪付
     */
    HX_UP(4, "hxb-1028-17");


    HXChannelEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }
    private final int value;
    private final String text;


    public String getText() {
        return this.text;
    }

    public int getValue() {
        return this.value;
    }
}
