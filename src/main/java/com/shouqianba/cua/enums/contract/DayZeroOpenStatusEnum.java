package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * D0开通状态枚举
 *
 * <AUTHOR>
 */
public enum DayZeroOpenStatusEnum implements ITextValueEnum<Integer> {

    /**
     * 未开通
     */
    NOT_OPENED(0, "未开通"),

    /**
     * 等待开通
     */
    WAITING_FOR_OPENING(1, "等待开通"),

    /**
     * 开通中
     */
    OPENING(2, "开通中"),

    /**
     * 开通成功
     */
    OPENED_SUCCESSFULLY(3, "开通成功"),

    /**
     * 开通失败
     */
    OPENING_FAILED(4, "开通失败");


    private final Integer value;
    private final String text;

    DayZeroOpenStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
