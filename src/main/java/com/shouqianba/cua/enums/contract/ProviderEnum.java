package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 结算通道类型枚举
 *
 * <AUTHOR>
 * @date 2023/12/21
 */
public enum ProviderEnum implements ITextValueEnum<Integer> {

    ALI_PAY(2, "支付宝"),
    WEI_XIN(3, "微信"),
    UNIONPAY(17, "云闪付"),
    BESTPAY(18, "翼支付"),
    WEIXIN_HK(19, "微信香港本地支付"),
    ALIPAY_INTL(20, "支付宝国际版"),
    PROVIDER_CIBBANK(1001, "兴业银行"),
    PROVIDER_LAKALA(1002, "拉卡拉"),
    PROVIDER_CITICBANK(1003, "中信银行"),
    PROVIDER_CIBGZBANK(1008, "广州兴业银行"),
    PROVIDER_CMBC(1010, "中国民生银行"),
    PROVIDER_LKLWANMA(1011, "拉卡拉万码账户方模式"),
    PROVIDER_NUCC(1013, "网联"),
    PROVIDER_UNIONPAY(1014, "银联"),
    PROVIDER_CIBSHBANK(1015, "上海兴业"),
    PROVIDER_DIRECT_UNIONPAY(1016, "银联-直连"),
    PROVIDER_UION_OPEN(1017, "银联开放平台"),
    PROVIDER_UMS(1018, "银联商务"),
    PROVIDER_TONGLIAN(1020, "通联"),
    PROVIDER_PSBC(1023, "邮储"),
    PROVIDER_CGB(1024, "广发"),
    PROVIDER_CCB(1026, "建行"),
    PROVIDER_HXB(1028, "华夏"),
    PROVIDER_ICBC(1030, "工商银行"),
    PROVIDER_LAKALA_V3(1032, "拉卡拉v3"),
    PROVIDER_LKLORG(1033, "拉卡拉渠道"),

    PROVIDER_LKL_OPEN(1034, "拉卡拉开放平台"),
    PROVIDER_TONGLIAN_V2(1035, "通联收银宝"),
    PROVIDER_HAIKE(1037, "海科"),
    PROVIDER_FUYOU(1038, "富友"),
    PROVIDER_PAB(1040, "平安银行"),
    PROVIDER_ZJTLCB(1043, "浙江泰隆银行"),
    PROVIDER_FJNX(1044, "福建农信"),
    PROVIDER_SPDB(1046, "浦发银行"),
    PROVIDER_JSB(1047, "江苏银行"),
    PROVIDER_GUOTONG(1048, "国通星驿"),
    PROVIDER_LZB(1049, "泸州银行"),
    PROVIDER_UMB(1050, "中投科信"),
    PROVIDER_YXT(1061, "院校通"),
    PROVIDER_PSBCSX(1054,"邮储银行山西分行"),
    PROVIDER_BOC(1053,"中国银行"),
    PROVIDER_BCS(1064, "长沙银行"),
    PROVIDER_LEXIN(1069, "乐信"),
    ;

    ProviderEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    private final Integer value;

    private final String text;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
