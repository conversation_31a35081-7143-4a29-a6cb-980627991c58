package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 富友进件结算类型枚举
 *
 * <AUTHOR>
 */
public enum FuYouContractSettleTypeEnum implements ITextValueEnum<Integer> {

    AUTO_SETTLEMENT_T1(1, "自动结算-T1"),

    MANUAL_SETTLEMENT_T1(2, "手动结算-T1转结"),

    AUTO_SETTLEMENT_D1(3, "自动结算-D1"),

    TIMING_SETTLEMENT(4, "定时结算"),

    MANUAL_SETTLEMENT_D1(5, "手动结算-D1转结");

    private final Integer value;

    private final String text;

    FuYouContractSettleTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
