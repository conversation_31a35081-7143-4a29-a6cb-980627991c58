package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 富友业务审核变更状态枚举
 *
 * <AUTHOR>
 */
public enum FuYouBusinessAuditModifyStatusEnum implements ITextValueEnum<String> {

    /**
     * 已拒绝
     */
    REFUSED("00", "已拒绝"),

    /**
     * 已创建待提交
     */
    CREATED("01", "已创建待提交"),

    /**
     * 已提交待审核
     */
    SUBMITTED("02", "已提交待审核"),

    /**
     * 已审核待批准
     */
    AUDITED("03", "已审核待批准"),

    /**
     * 待渠道处理
     */
    WAIT_CHANNEL_PROCESS("08", "待渠道处理"),

    /**
     * 已处理完成
     */
    PROCESSED("04", "已处理完成");;

    private final String value;
    private final String text;

    FuYouBusinessAuditModifyStatusEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
