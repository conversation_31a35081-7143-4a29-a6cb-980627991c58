package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 影响主任务状态枚举
 *
 * <AUTHOR>
 */
public enum AffectPrimaryTaskStatusEnum implements ITextValueEnum<Integer> {

    NO(0, "不影响"),

    YES(1, "影响");

    private final Integer value;
    private final String text;

    AffectPrimaryTaskStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
