package com.shouqianba.cua.enums.contract;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 内部调度子任务状态枚举
 *
 * <AUTHOR>
 */
public enum InternalScheduleSubTaskStatusEnum implements ITextValueEnum<Integer> {

    /**
     * 待处理
     */
    WAIT_PROCESS(0, "待处理"),

    /**
     * 处理中
     */
    BEING_PROCESSING(1, "处理中"),

    /**
     * 等待外部结果
     */
    WAIT_EXTERNAL_RESULT(2, "等待外部结果"),

    /**
     * 重试
     */
    RETRY(3, "重试"),

    /**
     * 处理失败
     */
    PROCESS_FAIL(4, "处理失败"),

    /**
     * 处理成功
     */
    PROCESS_SUCCESS(5, "处理成功");


    private final Integer value;
    private final String text;

    InternalScheduleSubTaskStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
