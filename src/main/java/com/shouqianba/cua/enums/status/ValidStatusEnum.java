package com.shouqianba.cua.enums.status;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 有效状态枚举
 *
 * <AUTHOR>
 */
public enum ValidStatusEnum implements ITextValueEnum<Integer> {
    INVALID(0, "失效"),
    VALID(1, "生效");

    private final int value;
    private final String text;

    private ValidStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getText() {
        return this.text;
    }

    public Integer getValue() {
        return this.value;
    }
}

