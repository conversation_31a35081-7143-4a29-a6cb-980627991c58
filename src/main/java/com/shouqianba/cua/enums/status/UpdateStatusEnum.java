package com.shouqianba.cua.enums.status;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 更新状态枚举
 *
 * <AUTHOR>
 */
public enum UpdateStatusEnum implements ITextValueEnum<Integer> {
    /**
     * 未知
     */
    UN_KNOWN(-1, "未知"),
    /**
     * 失败
     */
    FAIL(0, "失败"),
    /**
     * 成功
     */
    SUCCESS(1, "成功");

    private final int value;
    private final String text;

    UpdateStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
