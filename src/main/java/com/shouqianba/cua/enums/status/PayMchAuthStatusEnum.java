package com.shouqianba.cua.enums.status;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 支付源商户号授权状态枚举
 *
 * <AUTHOR>
 */
public enum PayMchAuthStatusEnum implements ITextValueEnum<Integer> {
    /**
     * 未授权
     */
    NOT(0, "未授权"),
    /**
     * 已授权
     */
    YES(1, "已授权");

    private final int value;
    private final String text;

    PayMchAuthStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
