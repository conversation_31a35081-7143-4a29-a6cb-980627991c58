package com.shouqianba.cua.enums.status;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 禁用状态枚举
 *
 * <AUTHOR>
 */
public enum DisableStatusEnum implements ITextValueEnum<Integer> {

    /**
     * 禁用
     */
    DISABLE(0, "禁用"),

    /**
     * 启用
     */
    ACTIVATE(1, "启用");

    private final int value;
    private final String text;

    DisableStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
