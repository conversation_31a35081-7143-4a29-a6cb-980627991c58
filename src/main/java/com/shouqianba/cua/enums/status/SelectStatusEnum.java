package com.shouqianba.cua.enums.status;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 选择状态枚举
 *
 * <AUTHOR>
 */
public enum SelectStatusEnum implements ITextValueEnum<Integer> {

    ENABLE(1, "可选择"),

    UNABLE(2, "不可选择"),

    ONLY_CAN(3, "只可选择");

    private final int value;
    private final String text;

    private SelectStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getText() {
        return this.text;
    }

    public Integer getValue() {
        return this.value;
    }
}

