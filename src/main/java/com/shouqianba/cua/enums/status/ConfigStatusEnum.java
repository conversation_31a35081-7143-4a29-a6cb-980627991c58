package com.shouqianba.cua.enums.status;

import com.shouqianba.cua.annotation.ITextValueEnum;


/**
 * 配置状态枚举
 *
 * <AUTHOR>
 */
public enum ConfigStatusEnum implements ITextValueEnum<Integer> {
    /**
     * 未配置
     */
    NOT_CONFIG(0, "未配置"),
    /**
     * 配置成功
     */
    CONFIG_SUCCESS(1, "配置成功"),
    /**
     * 不需要配置
     */
    NOT_REQUIRE_CONFIG(2, "不需要配置");

    private final int value;
    private final String text;

    ConfigStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
