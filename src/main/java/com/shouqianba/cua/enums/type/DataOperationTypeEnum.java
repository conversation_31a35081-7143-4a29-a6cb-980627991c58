package com.shouqianba.cua.enums.type;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 数据操作类型枚举
 *
 * <AUTHOR>
 */
public enum DataOperationTypeEnum implements ITextValueEnum<Integer> {
    /**
     * 插入操作
     */
    INSERT(1, "INSERT"),
    /**
     * 更新操作
     */
    UPDATE(2, "UPDATE"),
    /**
     * 删除操作
     */
    DELETE(3, "DELETE"),
    /**
     * 数据定义改变
     */
    ALERT(4, "ALERT");

    private final int value;
    private final String text;

    private DataOperationTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    public String getText() {
        return this.text;
    }

    public Integer getValue() {
        return this.value;
    }
}

