package com.shouqianba.cua.enums.businesslog;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public enum LogPlatformEnum {

    SPA("SPA","SPA", Lists.newArrayList("sp","SP", "SPA")),
    CRM_WEB("CRM WEB", "CRM WEB", Lists.newArrayList("CRM WEB", "crm web", "CRM_WEB", "crm_web")),
    CRM_APP("CRM APP", "CRM APP", Lists.newArrayList("CRM APP", "crm app", "CRM_APP", "crm_app")),
    APP("收钱吧APP", "收钱吧APP", Lists.newArrayList("收钱吧APP", "app", "APP")),
    CRM("CRM", "CRM", Lists.newArrayList("crm", "CRM")),
    MSP("MSP","MSP", Lists.newArrayList("MSP", "msp"))
    ;

    LogPlatformEnum(String code, String name, List<String> aliasList){
        this.code = code;
        this.name = name;
        this.aliasList = aliasList;
    }

    public static String getName(String code){
        LogPlatformEnum[] values= LogPlatformEnum.values();
        for(LogPlatformEnum value : values){
            if(StringUtils.equals(value.getCode(), code)){
                return value.getName();
            }
        }
        return StringUtils.EMPTY;
    }

    public static String getCode(String name){
        LogPlatformEnum[] values= LogPlatformEnum.values();
        for(LogPlatformEnum value : values){
            if(value.getName().equals(name)){
                return value.getCode();
            }
        }
        return StringUtils.EMPTY;
    }

    public static LogPlatformEnum getEnumByCode(String code){
        LogPlatformEnum[] values= LogPlatformEnum.values();
        for(LogPlatformEnum value : values){
            if(StringUtils.equals(value.getCode(), code)){
                return value;
            }
        }
        return null;
    }

    public static LogPlatformEnum getEnumByAliasList(String code) {

        LogPlatformEnum[] values = LogPlatformEnum.values();
        for(LogPlatformEnum value : values){
            if(value.getAliasList().contains(code)){
                return value;
            }
        }
        return null;
    }

    private final String code;

    private final String name;

    private final List<String> aliasList;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public List<String> getAliasList() {
        return aliasList;
    }
}
