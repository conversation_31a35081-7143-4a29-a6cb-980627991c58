package com.shouqianba.cua.utils.json;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class JSONTest {

    @Test
    public void testToJSONString() {
        Student student = new Student();
        student.setName("jack");
        student.setAge(20);
        String jsonString = JSON.toJSONString(student);
        assertEquals("{\"name\":\"jack\",\"age\":20}", jsonString);
    }

    @Test
    public void testParseObject() {
        String expectedValue = "{\"name\":\"jack\",\"age\":20}";
        Student student = JSON.parseObject(expectedValue, Student.class);
        assertEquals("jack", student.getName());
        assertEquals(Integer.valueOf(20), student.getAge());
    }

    @Test
    public void testParseTypeReferenceObject() {
        String inputJson = "[{\"dataList\":[{\"name\":\"jack\",\"age\":20}],\"dataType\":\"学生\"}," +
                "{\"dataList\":[{\"name\":\"Mary\",\"age\":31}],\"dataType\":\"老师\"}]";
        List<ComplexObjectInfo<Student>> complexObjectInfoList = JSON.parseObject(inputJson, new TypeReference<List<ComplexObjectInfo<Student>>>() {
        });
        assertEquals(2, complexObjectInfoList.size());
        assertEquals("学生", complexObjectInfoList.get(0).getDataType());
        assertEquals("jack", complexObjectInfoList.get(0).getDataList().get(0).getName());
        assertEquals(Integer.valueOf(20), complexObjectInfoList.get(0).getDataList().get(0).getAge());
        assertEquals("老师", complexObjectInfoList.get(1).getDataType());
        assertEquals("Mary", complexObjectInfoList.get(1).getDataList().get(0).getName());
        assertEquals(Integer.valueOf(31), complexObjectInfoList.get(1).getDataList().get(0).getAge());
    }

    @Test
    public void testParseByteArrayTypeReferenceObject() {
        String inputJson = "[{\"dataList\":[{\"name\":\"jack\",\"age\":20, \"sex\": \"男\"}],\"dataType\":\"学生\"}," +
                "{\"dataList\":[{\"name\":\"Mary\",\"age\":31}],\"dataType\":\"老师\"}]";
        byte[] bytes = inputJson.getBytes(StandardCharsets.UTF_8);
        List<ComplexObjectInfo<Student>> complexObjectInfoList =
                JSON.parseObject(bytes, StandardCharsets.UTF_8, new TypeReference<List<ComplexObjectInfo<Student>>>() {
                });
        assertEquals(2, complexObjectInfoList.size());
        assertEquals("学生", complexObjectInfoList.get(0).getDataType());
        assertEquals("jack", complexObjectInfoList.get(0).getDataList().get(0).getName());
        assertEquals(Integer.valueOf(20), complexObjectInfoList.get(0).getDataList().get(0).getAge());
        assertEquals("老师", complexObjectInfoList.get(1).getDataType());
        assertEquals("Mary", complexObjectInfoList.get(1).getDataList().get(0).getName());
        assertEquals(Integer.valueOf(31), complexObjectInfoList.get(1).getDataList().get(0).getAge());
    }

    @Test
    public void testParseArray() {
        String expectedValue = "[{\"name\":\"jack\",\"age\":20}]";
        List<Student> students = JSON.parseArray(expectedValue, Student.class);
        assertEquals("jack", students.get(0).getName());
        assertEquals(Integer.valueOf(20), students.get(0).getAge());
    }


    @Test
    public void convertValue() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", "jack");
        map.put("age", "20");
        Student student = JSON.convertValue(map, Student.class);
        System.out.println(student);
        assertEquals(student.getName(), map.get("name"));
    }

    @Test
    public void readTree() {
        String expectedValue = "{\n" +
                "    \"childrenRulesDecisionNodeList\": [\n" +
                "      {\n" +
                "        \"childrenRulesDecisionNodeList\": [],\n" +
                "        \"createTime\": 1709603926677,\n" +
                "        \"groupRouteRuleDetailRspDTOList\": [\n" +
                "          {\n" +
                "            \"createTime\": 1709603927158,\n" +
                "            \"id\": 213,\n" +
                "            \"logicalOperationType\": \"IN\",\n" +
                "            \"objectPropertyDTOList\": [],\n" +
                "            \"objectPropertyType\": \"cityCode\",\n" +
                "            \"objectPropertyValue\": \"[\\\"440900\\\"]\",\n" +
                "            \"ruleDecisionId\": 202,\n" +
                "            \"updateTime\": 1709603927158,\n" +
                "            \"validStatus\": 1\n" +
                "          }\n" +
                "        ],\n" +
                "        \"id\": 202,\n" +
                "        \"name\": \"市选择规则\",\n" +
                "        \"parentId\": 201,\n" +
                "        \"ruleDetailConnectionType\": \"AND\",\n" +
                "        \"updateTime\": 1709603926677,\n" +
                "        \"validStatus\": 1\n" +
                "      },\n" +
                "      {\n" +
                "        \"childrenRulesDecisionNodeList\": [\n" +
                "          {\n" +
                "            \"childrenRulesDecisionNodeList\": [],\n" +
                "            \"createTime\": 1709603928316,\n" +
                "            \"groupRouteRuleDetailRspDTOList\": [\n" +
                "              {\n" +
                "                \"createTime\": 1709603928947,\n" +
                "                \"id\": 214,\n" +
                "                \"logicalOperationType\": \"IN\",\n" +
                "                \"objectPropertyDTOList\": [],\n" +
                "                \"objectPropertyType\": \"type\",\n" +
                "                \"objectPropertyValue\": \"[\\\"1\\\",\\\"2\\\"]\",\n" +
                "                \"ruleDecisionId\": 204,\n" +
                "                \"updateTime\": 1709603928947,\n" +
                "                \"validStatus\": 1\n" +
                "              }\n" +
                "            ],\n" +
                "            \"id\": 204,\n" +
                "            \"name\": \"小微个体\",\n" +
                "            \"parentId\": 203,\n" +
                "            \"ruleDetailConnectionType\": \"AND\",\n" +
                "            \"updateTime\": 1709603928316,\n" +
                "            \"validStatus\": 1\n" +
                "          },\n" +
                "          {\n" +
                "            \"childrenRulesDecisionNodeList\": [],\n" +
                "            \"createTime\": *************,\n" +
                "            \"groupRouteRuleDetailRspDTOList\": [\n" +
                "              {\n" +
                "                \"createTime\": *************,\n" +
                "                \"id\": 215,\n" +
                "                \"logicalOperationType\": \"IN\",\n" +
                "                \"objectPropertyDTOList\": [],\n" +
                "                \"objectPropertyType\": \"type\",\n" +
                "                \"objectPropertyValue\": \"[\\\"3\\\",\\\"4\\\"]\",\n" +
                "                \"ruleDecisionId\": 205,\n" +
                "                \"updateTime\": *************,\n" +
                "                \"validStatus\": 1\n" +
                "              },\n" +
                "              {\n" +
                "                \"createTime\": *************,\n" +
                "                \"id\": 216,\n" +
                "                \"logicalOperationType\": \"EQUAL\",\n" +
                "                \"objectPropertyDTOList\": [],\n" +
                "                \"objectPropertyType\": \"bankAccountType\",\n" +
                "                \"objectPropertyValue\": \"2\",\n" +
                "                \"ruleDecisionId\": 205,\n" +
                "                \"updateTime\": *************,\n" +
                "                \"validStatus\": 1\n" +
                "              }\n" +
                "            ],\n" +
                "            \"id\": 205,\n" +
                "            \"name\": \"企业组织对公\",\n" +
                "            \"parentId\": 203,\n" +
                "            \"ruleDetailConnectionType\": \"AND\",\n" +
                "            \"updateTime\": *************,\n" +
                "            \"validStatus\": 1\n" +
                "          }\n" +
                "        ],\n" +
                "        \"createTime\": *************,\n" +
                "        \"groupRouteRuleDetailRspDTOList\": [],\n" +
                "        \"id\": 203,\n" +
                "        \"name\": \"小微个体或(企业组织对公)\",\n" +
                "        \"parentId\": 201,\n" +
                "        \"ruleConnectionType\": \"OR\",\n" +
                "        \"updateTime\": *************,\n" +
                "        \"validStatus\": 1\n" +
                "      }\n" +
                "    ],\n" +
                "    \"chooseType\": \"ENABLE\",\n" +
                "    \"createTime\": 1709603926107,\n" +
                "    \"groupRouteRuleDetailRspDTOList\": [],\n" +
                "    \"groupStrategyId\": 4,\n" +
                "    \"id\": 201,\n" +
                "    \"name\": \"市选择规则+(小微个体或者企业组织对公)主富有备拉卡拉\",\n" +
                "    \"parentId\": 0,\n" +
                "    \"priority\": 210,\n" +
                "    \"remark\": \"茂名市\",\n" +
                "    \"ruleConnectionType\": \"AND\",\n" +
                "    \"updateTime\": 1709603926107,\n" +
                "    \"validStatus\": 1\n" +
                "  }";
        JsonNode jsonNode = JSON.readTree(expectedValue);
        assertEquals("市选择规则", jsonNode.get("childrenRulesDecisionNodeList").get(0).get("name").asText());

    }
}
