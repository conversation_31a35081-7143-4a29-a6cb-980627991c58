package com.shouqianba.cua.utils.encrypt;

import org.junit.Test;

import static junit.framework.TestCase.assertEquals;

public class MD5UtilsTest {

    @Test
    public void testGetMD5String() {
        String md5Code = MD5Utils.getMD5String("Kafka 提供了消费者客户端参数 partition.assignment.strategy 来设置消费者与订阅主题之间的分区分配策略。默认情况下，此参数的值为 org.apache.kafka.clients.consumer.RangeAssignor，即采用 RangeAssignor 分配策略。除此之外，Kafka 还提供了另外两种分配策略：RoundRobinAssignor 和 StickyAssignor。消费者客户端参数 partition.assignment.strategy 可以配置多个分配策略，彼此之间以逗号分隔");
        assertEquals(md5Code, "fa3750b452035a95a5d316ec83705081");
    }
}
