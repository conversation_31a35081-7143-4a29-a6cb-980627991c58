package com.shouqianba.cua.utils.thread;

import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Test;

import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static junit.framework.TestCase.*;

@SuppressWarnings("all")
public class ThreadPoolWorkerTest {

    @Test
    public void test() {
        ForkJoinPool forkJoinPool = new ForkJoinPool(5);
        List<Object> result = ThreadPoolWorker.of(forkJoinPool)
                .addWork(() -> {
                    try {
                        Thread.sleep(3000);
                        System.out.println("thread" + Thread.currentThread().getName());
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return 1;
                })
                .addWork(() -> {
                    try {
                        Thread.sleep(10000);
                        System.out.println("thread" + Thread.currentThread().getName());
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return 2;
                })
                .addWork(() -> {
                    try {
                        Thread.sleep(3000);
                        System.out.println("thread" + Thread.currentThread().getName());
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return 3;
                })
                .addWork(() -> {
                    try {
                        Thread.sleep(3000);
                        System.out.println("thread" + Thread.currentThread().getName());

                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return 4;
                }).doWorks();
        System.out.println(result);
    }


    @Test
    public void test3() {
        ExecutorService executorService = Executors.newFixedThreadPool(4);
        List<Object> result = ThreadPoolWorker.of(executorService)
                .addWork("hello world", (input) -> {
                    System.out.println("get input: " + input + " threadId: " + Thread.currentThread().getName());
                    assertEquals("hello world", input);
                })
                .addWork(1, (input) -> input + 1)
                .doWorks();
        System.out.println(result);
    }

    @Test
    public void test4() {
        ExecutorService executorService = Executors.newFixedThreadPool(4);
        ThreadPoolWorker<Object> of = ThreadPoolWorker.of(executorService);
        long startTime = System.currentTimeMillis();
        CompletableFuture<Integer> submit1 = of
                .submit(() -> {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return 1;
                });
        CompletableFuture<Integer> submit2 = of
                .submit(() -> {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return 2;
                });
        CompletableFuture<Integer> submit3 = of
                .submit(3, (input) -> {
                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return input;
                });
        of.doWorks(submit1, submit2, submit3);
        Integer join = submit1.join();
        Integer join2 = submit2.join();
        Integer join3 = submit3.join();
        long endTime = System.currentTimeMillis();
        long takeTime = endTime - startTime;
        System.out.println("take time: " + takeTime);
        assertEquals(1, (int) join);
        assertEquals(2, (int) join2);
        assertEquals(3, (int) join3);
        assertTrue(takeTime < 3500);
    }

    @Test
    public void test5() {
        ExecutorService executorService = Executors.newFixedThreadPool(4);
        ThreadPoolWorker<Object> of = ThreadPoolWorker.of(executorService);
        long startTime = System.currentTimeMillis();
        CompletableFuture<Integer> submit1 = of
                .submit(() -> {
                    try {
                        System.out.println("submit1");
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return 1;
                });
        CompletableFuture<Integer> submit2 = of
                .submit(() -> {
                    try {
                        System.out.println("submit2");
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    return 2;
                });
        CompletableFuture<Integer> submit3 = of
                .submit(() -> {
                    System.out.println("submit3");
                    return 3;
                    // throw new RuntimeException("test exception");
                });
        boolean hasException = false;
        try {
            of.doWorks(submit1, submit2, submit3);
            Integer join = submit1.join();
            Integer join2 = submit2.join();
            Integer join3 = submit3.join();
            System.out.println("join " + join);
            System.out.println("join2 " + join2);
            System.out.println("join3 " + join3);
            long endTime = System.currentTimeMillis();
            long takeTime = endTime - startTime;
            System.out.println("take time: " + takeTime);
        } catch (Exception ex) {
            hasException = true;
        }
        if (hasException) {
            fail();
        }

    }

    @Test
    public void testReturnSameType() {
        Set<Integer> result = ThreadPoolWorker.<List<Integer>>of()
                .addWork(() -> Arrays.asList(1, 2))
                .addWork(new Supplier<List<Integer>>() {
                    @Override
                    public List<Integer> get() {
                        return Arrays.asList(1, 2, 3, 4);
                    }
                })
                .addWork(() -> Arrays.asList(3, 4, 5))
                .doWorks()
                .stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        Assert.assertEquals(5, result.size());
    }

    @Test
    public void testSubmitWithCallBack() throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(4);
        ThreadPoolWorker<Object> of = ThreadPoolWorker.of(executorService);
        Function<String, String> function = new Function<String, String>() {
            @SneakyThrows
            @Override
            public String apply(String s) {
                Thread.sleep(1000 * 1);
                return "sqb";
            }
        };
        Callback<String> callback = new Callback<String>() {
            @Override
            public void onSuccess(String result) {
                System.out.println(result);
                System.out.println("回调处理成功, thread: " + Thread.currentThread().getName());
            }
            @Override
            public void onException(Throwable e) {

            }
        };
        of.submitWithCallBack("5", function, callback);
        System.out.println("任务已经提交...: " + Thread.currentThread().getName());
        Thread.sleep(1000 * 5);
        System.out.println("主程序运行结束...: " + Thread.currentThread().getName());
    }

    @Test
    public void testSubmitWithCallBackException() throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(4);
        ThreadPoolWorker<Object> of = ThreadPoolWorker.of(executorService);
        Function<String, String> function = new Function<String, String>() {
            @SneakyThrows
            @Override
            public String apply(String s) {
                Thread.sleep(1000 * 1);
                int i = 1/0;
                return "sqb";
            }
        };
        Callback<String> callback = new Callback<String>() {
            @Override
            public void onSuccess(String result) {
                System.out.println(result);
                System.out.println("回调处理成功, thread: " + Thread.currentThread().getName());
            }
            @Override
            public void onException(Throwable e) {
                System.out.println("exception: " + e.getMessage());
                System.out.println("回调处理异常, thread: " + Thread.currentThread().getName());

            }
        };
        of.submitWithCallBack("5", function, callback);
        System.out.println("任务已经提交...: " + Thread.currentThread().getName());
        Thread.sleep(1000 * 5);
        System.out.println("主程序运行结束...: " + Thread.currentThread().getName());
    }


    @Test
    public void testSubmitWithCallBackTimeout() throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        Function<String, String> function = new Function<String, String>() {
            @SneakyThrows
            @Override
            public String apply(String s) {
                return "sqb";
            }
        };
        Callback<String> callback = new Callback<String>() {
            @Override
            public void onSuccess(String result) {
                System.out.println(result);
                System.out.println("回调处理成功, thread: " + Thread.currentThread().getName());
            }
            @Override
            public void onException(Throwable e) {
                System.out.println("处理异常：" + e.getMessage());
            }
        };
        executorService.submit(new Runnable() {
            @SneakyThrows
            @Override
            public void run() {
                Thread.sleep(1000 * 4);
            }
        });
        ThreadPoolWorker<Object> of = ThreadPoolWorker.of(executorService);
        of.submitWithCallBack("5", function, callback, 1000);
        System.out.println("任务已经提交...: " + Thread.currentThread().getName());
        Thread.sleep(1000 * 10);
        System.out.println("主程序运行结束...: " + Thread.currentThread().getName());
    }

}
