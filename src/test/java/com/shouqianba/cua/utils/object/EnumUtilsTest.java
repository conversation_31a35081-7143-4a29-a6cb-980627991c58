package com.shouqianba.cua.utils.object;

import com.shouqianba.cua.enums.core.DeleteStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Optional;
import static junit.framework.TestCase.*;


/**
 * 枚举工具类单元测试
 *
 * <AUTHOR>
 * @date 2023/12/4 14:01
 */
@Slf4j
public class EnumUtilsTest {

    @Test
    public void testOf() {
        Optional<DeleteStatusEnum> deleteStatusEnum = EnumUtils.of(DeleteStatusEnum.class, 1);
        if (!deleteStatusEnum.isPresent()) {
            fail();
        }
        assertEquals(1, (int) deleteStatusEnum.get().getValue());
    }

    @Test
    public void testOfNullable() {
        Optional<DeleteStatusEnum> deleteStatusEnum = EnumUtils.ofNullable(DeleteStatusEnum.class, 3);
        assertFalse(deleteStatusEnum.isPresent());
    }

    @Test
    public void testListAll() {
        assertEquals(2, EnumUtils.listAllEnums(DeleteStatusEnum.class).size());
    }
}
