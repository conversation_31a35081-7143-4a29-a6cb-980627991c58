package com.shouqianba.cua.utils.object;

import com.shouqianba.cua.utils.stream.Student;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * BeanCopy 工具测试
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class BeanCopyUtilsTest {

    @Test
    public void testCopyProperties() {
        Student target = new Student();
        target.setName("shouqian");
        target.setAge(30);

        Student copy = BeanCopyUtils.copyProperties(target, Student.class);
        assertEquals(target.getName(), copy.getName());
        assertEquals(target.getAge(), copy.getAge());
    }

}
