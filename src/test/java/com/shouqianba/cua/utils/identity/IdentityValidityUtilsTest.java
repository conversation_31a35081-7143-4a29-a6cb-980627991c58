package com.shouqianba.cua.utils.identity;

import org.junit.Test;
import java.util.HashSet;
import java.util.Set;
import static org.junit.Assert.*;

/**
 * IdentityValidityUtils的单元测试类
 */
public class IdentityValidityUtilsTest {

    /**
     * 创建测试用的规则对象
     */
    private IdentityValidityUtils.Rule createTestRule() {
        IdentityValidityUtils.Rule rule = new IdentityValidityUtils.Rule();
        Set<Integer> validYearDiff = new HashSet<>();
        validYearDiff.add(5);
        validYearDiff.add(10);
        rule.setValidYearDiff(validYearDiff);
        rule.setDayOffsetTolerance(1);
        rule.setSupportLongTerm(true);
        rule.setMinRemainingDays(30);
        
        Set<String> uniqueValidity = new HashSet<>();
        uniqueValidity.add("0228-0229");
        uniqueValidity.add("0228-0301");
        uniqueValidity.add("0229-0228");
        rule.setUniqueValidity(uniqueValidity);
        
        return rule;
    }

    /**
     * 测试正常情况：有效证件（5年有效期）
     */
    @Test
    public void testValidIdentity5Years() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20250101-20300101");
        assertTrue("校验应该通过", result.isValid());
        assertNull("通过时不应该有失败原因", result.getReason());
    }

    /**
     * 测试正常情况：有效证件（10年有效期）
     */
    @Test
    public void testValidIdentity10Years() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200315-20300315");
        assertTrue("校验应该通过", result.isValid());
        assertNull("通过时不应该有失败原因", result.getReason());
    }

    /**
     * 测试长期有效证件
     */
    @Test
    public void testLongTermIdentity() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-99991231");
        assertTrue("长期有效证件应该通过校验", result.isValid());
        assertNull("通过时不应该有失败原因", result.getReason());
    }

    /**
     * 测试不支持长期有效但提供了长期有效日期的情况
     */
    @Test
    public void testLongTermNotSupported() {
        IdentityValidityUtils.Rule rule = createTestRule();
        rule.setSupportLongTerm(false);
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-99991231");
        assertFalse("不支持长期有效时应该校验失败", result.isValid());
        assertEquals("失败原因应该是年份差错误", "年份差错误", result.getReason());
    }

    /**
     * 测试特殊有效期：闰年情况（0228-0229）
     */
    @Test
    public void testLeapYearSpecialValidity() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200229-20300228");
        assertTrue("特殊有效期应该通过校验", result.isValid());
        assertNull("通过时不应该有失败原因", result.getReason());
    }

    /**
     * 测试有效期格式不正确的情况
     */
    @Test
    public void testInvalidFormat() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101_20250101");
        assertFalse("格式不正确应该校验失败", result.isValid());
        assertEquals("失败原因应该是格式错误", "有效期格式不正确，应为yyyyMMdd-yyyyMMdd格式", result.getReason());
    }

    /**
     * 测试有效期为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testEmptyValidity() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.checkValidity(rule, "");
    }

    /**
     * 测试规则为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testNullRule() {
        IdentityValidityUtils.checkValidity(null, "20200101-20250101");
    }

    /**
     * 测试日期解析错误的情况
     */
    @Test
    public void testInvalidDate() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20201301-20250101");
        assertFalse("日期错误应该校验失败", result.isValid());
        assertEquals("失败原因应该是日期错误", "日期错误", result.getReason());
    }

    /**
     * 测试年份差不符合要求的情况
     */
    @Test
    public void testInvalidYearDifference() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-20230101");
        assertFalse("年份差不符合要求应该校验失败", result.isValid());
        assertEquals("失败原因应该是年份差错误", "年份差错误", result.getReason());
    }

    /**
     * 测试有效期剩余天数不足的情况
     */
    @Test
    public void testInsufficientRemainingDays() {
        IdentityValidityUtils.Rule rule = createTestRule();
        // 设置一个即将过期的证件
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-20250101");
        assertFalse("有效期剩余天数不足应该校验失败", result.isValid());
        assertTrue("失败原因应该包含天数不足的信息", result.getReason().contains("证件有效期需大于"));
    }

    /**
     * 测试日期差超过容差的情况
     */
    @Test
    public void testExceedDayOffsetTolerance() {
        IdentityValidityUtils.Rule rule = createTestRule();
        // 日期差超过容差（20200101-20250105，日差为4，超过容差1）
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-20300105");
        assertFalse("日期差超过容差应该校验失败", result.isValid());
        assertEquals("失败原因应该是日期不匹配", "开始日期和结束日期不匹配", result.getReason());
    }

    /**
     * 测试正常情况：有效证件（10年有效期）
     */
    @Test
    public void testInValidDate() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200315-20300230");
        assertFalse("日期错误应该校验失败", result.isValid());
        assertEquals("失败原因应该是日期错误", "日期错误", result.getReason());
    }

    /**
     * 测试Rule字段为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testNullValidYearDiff() {
        IdentityValidityUtils.Rule rule = createTestRule();
        rule.setValidYearDiff(null);
        IdentityValidityUtils.checkValidity(rule, "20200101-20250101");
    }

    /**
     * 测试Rule字段为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testNullDayOffsetTolerance() {
        IdentityValidityUtils.Rule rule = createTestRule();
        rule.setDayOffsetTolerance(null);
        IdentityValidityUtils.checkValidity(rule, "20200101-20250101");
    }
}