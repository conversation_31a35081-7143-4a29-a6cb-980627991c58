package com.shouqianba.cua.utils.identity;

import org.junit.Test;
import java.util.HashSet;
import java.util.Set;
import static org.junit.Assert.*;

/**
 * IdentityValidityUtils的单元测试类
 */
public class IdentityValidityUtilsTest {

    /**
     * 创建测试用的规则对象
     */
    private IdentityValidityUtils.Rule createTestRule() {
        IdentityValidityUtils.Rule rule = new IdentityValidityUtils.Rule();
        Set<Integer> validYearDiff = new HashSet<>();
        validYearDiff.add(5);
        validYearDiff.add(10);
        rule.setValidYearDiff(validYearDiff);
        rule.setDayOffsetTolerance(1);
        rule.setSupportLongTerm(true);
        rule.setMinRemainingDays(30);
        
        Set<String> uniqueValidity = new HashSet<>();
        uniqueValidity.add("0228-0229");
        uniqueValidity.add("0228-0301");
        uniqueValidity.add("0229-0228");
        rule.setUniqueValidity(uniqueValidity);
        
        return rule;
    }

    /**
     * 测试正常情况：有效证件（5年有效期）
     */
    @Test
    public void testValidIdentity5Years() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20250101-20300101");
        assertTrue("校验应该通过", result.isValid());
        assertNull("通过时不应该有失败原因", result.getReason());
    }

    /**
     * 测试正常情况：有效证件（10年有效期）
     */
    @Test
    public void testValidIdentity10Years() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200315-20300315");
        assertTrue("校验应该通过", result.isValid());
        assertNull("通过时不应该有失败原因", result.getReason());
    }

    /**
     * 测试长期有效证件
     */
    @Test
    public void testLongTermIdentity() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-99991231");
        assertTrue("长期有效证件应该通过校验", result.isValid());
        assertNull("通过时不应该有失败原因", result.getReason());
    }

    /**
     * 测试不支持长期有效但提供了长期有效日期的情况
     */
    @Test
    public void testLongTermNotSupported() {
        IdentityValidityUtils.Rule rule = createTestRule();
        rule.setSupportLongTerm(false);
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-99991231");
        assertFalse("不支持长期有效时应该校验失败", result.isValid());
        assertEquals("失败原因应该是年份差错误", "年份差错误", result.getReason());
    }

    /**
     * 测试特殊有效期：闰年情况（0228-0229）
     */
    @Test
    public void testLeapYearSpecialValidity() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200229-20300228");
        assertTrue("特殊有效期应该通过校验", result.isValid());
        assertNull("通过时不应该有失败原因", result.getReason());
    }

    /**
     * 测试有效期格式不正确的情况
     */
    @Test
    public void testInvalidFormat() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101_20250101");
        assertFalse("格式不正确应该校验失败", result.isValid());
        assertEquals("失败原因应该是格式错误", "有效期格式不正确，应为yyyyMMdd-yyyyMMdd格式", result.getReason());
    }

    /**
     * 测试有效期为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testEmptyValidity() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.checkValidity(rule, "");
    }

    /**
     * 测试规则为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testNullRule() {
        IdentityValidityUtils.checkValidity(null, "20200101-20250101");
    }

    /**
     * 测试日期解析错误的情况
     */
    @Test
    public void testInvalidDate() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20201301-20250101");
        assertFalse("日期错误应该校验失败", result.isValid());
        assertEquals("失败原因应该是日期错误", "日期错误", result.getReason());
    }

    /**
     * 测试年份差不符合要求的情况
     */
    @Test
    public void testInvalidYearDifference() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-20230101");
        assertFalse("年份差不符合要求应该校验失败", result.isValid());
        assertEquals("失败原因应该是年份差错误", "年份差错误", result.getReason());
    }

    /**
     * 测试有效期剩余天数不足的情况
     */
    @Test
    public void testInsufficientRemainingDays() {
        IdentityValidityUtils.Rule rule = createTestRule();
        // 设置一个即将过期的证件
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-20250101");
        assertFalse("有效期剩余天数不足应该校验失败", result.isValid());
        assertTrue("失败原因应该包含天数不足的信息", result.getReason().contains("证件有效期需大于"));
    }

    /**
     * 测试日期差超过容差的情况
     */
    @Test
    public void testExceedDayOffsetTolerance() {
        IdentityValidityUtils.Rule rule = createTestRule();
        // 日期差超过容差（20200101-20250105，日差为4，超过容差1）
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200101-20300105");
        assertFalse("日期差超过容差应该校验失败", result.isValid());
        assertEquals("失败原因应该是日期不匹配", "开始日期和结束日期不匹配", result.getReason());
    }

    /**
     * 测试正常情况：有效证件（10年有效期）
     */
    @Test
    public void testInValidDate() {
        IdentityValidityUtils.Rule rule = createTestRule();
        IdentityValidityUtils.ValidationResult result = IdentityValidityUtils.checkValidity(rule, "20200315-20300230");
        assertFalse("日期错误应该校验失败", result.isValid());
        assertEquals("失败原因应该是日期错误", "日期错误", result.getReason());
    }

    /**
     * 测试Rule字段为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testNullValidYearDiff() {
        IdentityValidityUtils.Rule rule = createTestRule();
        rule.setValidYearDiff(null);
        IdentityValidityUtils.checkValidity(rule, "20200101-20250101");
    }

    /**
     * 测试Rule字段为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testNullDayOffsetTolerance() {
        IdentityValidityUtils.Rule rule = createTestRule();
        rule.setDayOffsetTolerance(null);
        IdentityValidityUtils.checkValidity(rule, "20200101-20250101");
    }

    /**
     * 创建启用闰日校验的测试规则
     */
    private IdentityValidityUtils.Rule createLeapDayTestRule() {
        IdentityValidityUtils.Rule rule = new IdentityValidityUtils.Rule();
        Set<Integer> validYearDiff = new HashSet<>();
        validYearDiff.add(5);
        validYearDiff.add(10);
        rule.setValidYearDiff(validYearDiff);
        rule.setDayOffsetTolerance(1);
        rule.setSupportLongTerm(false);
        rule.setMinRemainingDays(0); // 设置为0以便测试
        rule.setEnableLeapDayValidation(true); // 启用闰日校验
        return rule;
    }

    /**
     * 测试闰日特殊情况：开始日期是0228，结束日期在容忍范围内
     */
    @Test
    public void testLeapDaySpecialCase_StartDate0228() {
        IdentityValidityUtils.Rule rule = createLeapDayTestRule();

        // 开始日期是0228，结束日期在容忍范围内应该都通过
        assertTrue("0228-0227应该通过", IdentityValidityUtils.checkValidity(rule, "20200228-20250227").isValid());
        assertTrue("0228-0228应该通过", IdentityValidityUtils.checkValidity(rule, "20200228-20250228").isValid());
        assertTrue("0228-0229应该通过", IdentityValidityUtils.checkValidity(rule, "20200228-20250229").isValid());
        assertTrue("0228-0301应该通过", IdentityValidityUtils.checkValidity(rule, "20200228-20250301").isValid());
        assertTrue("0228-0302应该通过", IdentityValidityUtils.checkValidity(rule, "20200228-20250302").isValid());
    }

    /**
     * 测试闰日特殊情况：开始日期是0229，结束日期在容忍范围内
     */
    @Test
    public void testLeapDaySpecialCase_StartDate0229() {
        IdentityValidityUtils.Rule rule = createLeapDayTestRule();

        // 开始日期是0229，结束日期在容忍范围内应该都通过
        assertTrue("0229-0227应该通过", IdentityValidityUtils.checkValidity(rule, "20200229-20250227").isValid());
        assertTrue("0229-0228应该通过", IdentityValidityUtils.checkValidity(rule, "20200229-20250228").isValid());
        assertTrue("0229-0229应该通过", IdentityValidityUtils.checkValidity(rule, "20200229-20250229").isValid());
        assertTrue("0229-0301应该通过", IdentityValidityUtils.checkValidity(rule, "20200229-20250301").isValid());
        assertTrue("0229-0302应该通过", IdentityValidityUtils.checkValidity(rule, "20200229-20250302").isValid());
    }

    /**
     * 测试闰日特殊情况：结束日期是0228，开始日期在容忍范围内
     */
    @Test
    public void testLeapDaySpecialCase_EndDate0228() {
        IdentityValidityUtils.Rule rule = createLeapDayTestRule();

        // 结束日期是0228，开始日期在容忍范围内应该都通过
        assertTrue("0227-0228应该通过", IdentityValidityUtils.checkValidity(rule, "20200227-20250228").isValid());
        assertTrue("0228-0228应该通过", IdentityValidityUtils.checkValidity(rule, "20200228-20250228").isValid());
        assertTrue("0229-0228应该通过", IdentityValidityUtils.checkValidity(rule, "20200229-20250228").isValid());
        assertTrue("0301-0228应该通过", IdentityValidityUtils.checkValidity(rule, "20200301-20250228").isValid());
        assertTrue("0302-0228应该通过", IdentityValidityUtils.checkValidity(rule, "20200302-20250228").isValid());
    }

    /**
     * 测试闰日特殊情况：结束日期是0229，开始日期在容忍范围内
     */
    @Test
    public void testLeapDaySpecialCase_EndDate0229() {
        IdentityValidityUtils.Rule rule = createLeapDayTestRule();

        // 结束日期是0229，开始日期在容忍范围内应该都通过
        assertTrue("0227-0229应该通过", IdentityValidityUtils.checkValidity(rule, "20200227-20250229").isValid());
        assertTrue("0228-0229应该通过", IdentityValidityUtils.checkValidity(rule, "20200228-20250229").isValid());
        assertTrue("0229-0229应该通过", IdentityValidityUtils.checkValidity(rule, "20200229-20250229").isValid());
        assertTrue("0301-0229应该通过", IdentityValidityUtils.checkValidity(rule, "20200301-20250229").isValid());
        assertTrue("0302-0229应该通过", IdentityValidityUtils.checkValidity(rule, "20200302-20250229").isValid());
    }

    /**
     * 测试闰日校验关闭时的行为
     */
    @Test
    public void testLeapDayValidationDisabled() {
        IdentityValidityUtils.Rule rule = createLeapDayTestRule();
        rule.setEnableLeapDayValidation(false); // 关闭闰日校验

        // 这些情况应该按照正常的偏移量逻辑处理
        // 0228到0301的差距是1天，在容忍范围内，应该成功
        assertTrue("关闭闰日校验时，0228-0301应该通过", IdentityValidityUtils.checkValidity(rule, "20200228-20250301").isValid());

        // 0228到0302的差距是2天，超出了容忍范围，应该失败
        assertFalse("关闭闰日校验时，0228-0302应该失败", IdentityValidityUtils.checkValidity(rule, "20200228-20250302").isValid());
    }

    /**
     * 测试非闰日相关日期不受影响
     */
    @Test
    public void testNormalDatesNotAffected() {
        IdentityValidityUtils.Rule rule = createLeapDayTestRule();

        // 正常的日期应该正常工作
        assertTrue("正常日期应该通过", IdentityValidityUtils.checkValidity(rule, "20200101-20250101").isValid());
        assertTrue("正常日期偏移1天应该通过", IdentityValidityUtils.checkValidity(rule, "20200101-20250102").isValid());
        assertFalse("正常日期偏移2天应该失败", IdentityValidityUtils.checkValidity(rule, "20200101-20250103").isValid());
    }
}