package com.shouqianba.cua.utils.page;

import com.shouqianba.cua.model.page.NormalPagingResult;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;



/**
 * 逻辑分页工具类测试
 *
 * <AUTHOR>
 * @date 2024/3/29 08:40
 */
public class LogicPageUtilsTest {

    @Test
    public void testPageWithData() {
        List<String> testData = generateTestData();
        int pageNum = 1;
        int pageSize = 10;

        List<String> expectedPageData = testData.subList(0, 10);
        List<String> actualPageData = LogicPageUtils.page(pageNum, pageSize, testData);

        Assert.assertEquals(expectedPageData, actualPageData);
    }

    @Test
    public void testPageWithPagingResultWithData() {
        List<String> testData = generateTestData();
        int pageNum = 1;
        int pageSize = 10;
        NormalPagingResult<String> result = LogicPageUtils.pageWithPagingResult(pageNum, pageSize, testData);
        Assert.assertTrue("应该存在下一页", result.isHasNextPage());
        Assert.assertEquals("页码数据应为10条", 10, result.getList().size());
        Assert.assertEquals("页码应为1", 1, result.getPageNum());
        Assert.assertEquals("每页大小应为10", 10, result.getPageSize());
        Assert.assertEquals("总数据条数应为100", 100, result.getTotal());
        Assert.assertEquals("总页数应为10", 10, result.getPages());
    }

    @Test
    public void testPages() {
        List<String> testData = generateTestData();
        int pageNum = 7;
        int pageSize = 9;
        NormalPagingResult<String> result = LogicPageUtils.pageWithPagingResult(pageNum, pageSize, testData);
        Assert.assertTrue("应该存在下一页", result.isHasNextPage());
        Assert.assertEquals("页码数据应为9条", 9, result.getList().size());
        Assert.assertEquals("页码应为7", 7, result.getPageNum());
        Assert.assertEquals("每页大小应为9", 9, result.getPageSize());
        Assert.assertEquals("总数据条数应为100", 100, result.getTotal());
        Assert.assertEquals("总页数应为12", 12, result.getPages());
    }

    @Test
    public void testPageWithEmptyData() {
        List<String> emptyTestData = Collections.emptyList();
        int pageNum = 1;
        int pageSize = 10;
        List<String> actualPageData = LogicPageUtils.page(pageNum, pageSize, emptyTestData);
        Assert.assertTrue("分页数据应为空", actualPageData.isEmpty());
    }

    @Test
    public void testPageWithPagingResultWithEmptyData() {
        List<String> emptyTestData = Collections.emptyList();
        int pageNum = 1;
        int pageSize = 10;
        NormalPagingResult<String> result = LogicPageUtils.pageWithPagingResult(pageNum, pageSize, emptyTestData);
        Assert.assertFalse("不应存在下一页", result.isHasNextPage());
        Assert.assertTrue("分页数据应为空", result.getList().isEmpty());
        Assert.assertEquals("页码应为1", 1, result.getPageNum());
        Assert.assertEquals("每页大小应为10", 10, result.getPageSize());
        Assert.assertEquals("总数据条数应为0", 0, result.getTotal());
        Assert.assertEquals("总页数应为0", 0, result.getPages());
    }

    private List<String> generateTestData() {
        List<String> testData = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            testData.add("Data " + i);
        }
        return testData;
    }
}
